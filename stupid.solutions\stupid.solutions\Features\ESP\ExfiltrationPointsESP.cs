﻿using System.Collections.Generic;
using System.Net;
using Comfort.Common;
using EFT;
using EFT.Interactive;
using EFT.InventoryLogic;
using stupid.solutions.Data;
using stupid.solutions.Utils;
using JsonType;
using UnityEngine;

namespace stupid.solutions.Features.ESP
{
    public class ExfiltrationPointsESP : MonoBehaviour
    {
        private List<GameExfiltrationPoint> _gameExfiltrationPoints= new List<GameExfiltrationPoint>();
        private static readonly float CacheExfiltrationPointInterval = 5f;
        private float _nextLootItemCacheTime;

        private static readonly Color ExfiltrationPointColour = Color.green;

        public void Update()
        {
            

            if (Time.time >= _nextLootItemCacheTime)
            {
                if ((Main.GameWorld != null) && (Main.GameWorld.ExfiltrationController.ExfiltrationPoints != null))
                {
                    _gameExfiltrationPoints.Clear();
                    foreach (var exfiltrationPoint in Main.GameWorld.ExfiltrationController.ExfiltrationPoints)
                    {
                        if (!GameUtils.IsExfiltrationPointValid(exfiltrationPoint))
                            continue;

                        _gameExfiltrationPoints.Add(new GameExfiltrationPoint(exfiltrationPoint));
                    }

                    _nextLootItemCacheTime = (Time.time + CacheExfiltrationPointInterval);
                }
            }
            if (Main.GameWorld != null)
            {
                foreach (GameExfiltrationPoint gameExfiltrationPoint in _gameExfiltrationPoints)
                    gameExfiltrationPoint.RecalculateDynamics();
            }
            

        }
        private void OnGUI()
        {
            if (Main.GameWorld != null)
            {
                if (Settings.DrawExfiltrationPoints)
                {
                    foreach (var exfiltrationPoint in _gameExfiltrationPoints)
                    {
                        if (!GameUtils.IsExfiltrationPointValid(exfiltrationPoint.ExfiltrationPoint) || !exfiltrationPoint.IsOnScreen)
                            continue;

                        string exfiltrationPointText = $"{exfiltrationPoint.ExfiltrationPoint.Settings.Name} [{exfiltrationPoint.FormattedDistance}]";

                        Render.DrawString(new Vector2(exfiltrationPoint.ScreenPosition.x - 50f, exfiltrationPoint.ScreenPosition.y), exfiltrationPointText, ExfiltrationPointColour);
                    }
                }
                if(Main.LocalPlayerWeapon != null && Main.LocalPlayer != null)
                {
                    if (Settings.DrawInfo)
                    {
                        string tempMag = string.Empty;

                        if (Main.LocalPlayer.HandsController.Item is Weapon)
                        {
                            var weapon = Main.LocalPlayerWeapon;
                            var mag = weapon.GetCurrentMagazine();
                            if (mag != null)
                            {
                                tempMag = $" {mag.Count + weapon.ChamberAmmoCount}/{mag.MaxCount + weapon.ChamberAmmoCount} ";
                            }
                        }
                        else
                        {
                            tempMag = "";
                        }

                        string tempHealth = $"{Main.LocalPlayer.HealthController.GetBodyPartHealth(EBodyPart.Common, true).Current} / {Main.LocalPlayer.HealthController.GetBodyPartHealth(EBodyPart.Common, true).Maximum}";
                        float centerX = Screen.width / 2;
                        float centerY = Screen.height / 2;
                        Render.DrawString(new Vector2(centerX, centerY + 60f), tempMag, Color.red);
                    }
                }


            }
        }                                                       
    }
}
