﻿using EFT.Interactive;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace stupid.solutions.Utils
{
    class GameLootContainers
    {
        public LootableContainer LootableContainer { get; }
        public Vector3 ScreenPosition => _screenPosition;

        public bool IsOnScreen { get; private set; }

        public float Distance { get; private set; }

        public string FormattedDistance => $"{Math.Round(Distance)}m";

        private Vector3 _screenPosition;

        public GameLootContainers(LootableContainer lootableContainer)
        {
            if (lootableContainer == null)
                throw new ArgumentNullException(nameof(lootableContainer));

            LootableContainer = lootableContainer;
            _screenPosition = default;
            Distance = 0f;
        }

        public void RecalculateDynamics()
        {
            if (!GameUtils.IsLootableContainerValid(LootableContainer))
                return;
            _screenPosition = GameUtils.WorldPointToScreenPoint(LootableContainer.transform.position);
            IsOnScreen = GameUtils.IsScreenPointVisible(_screenPosition);
            Distance = Vector3.Distance(Main.MainCamera.transform.position, LootableContainer.transform.position);
        }
    }
}
