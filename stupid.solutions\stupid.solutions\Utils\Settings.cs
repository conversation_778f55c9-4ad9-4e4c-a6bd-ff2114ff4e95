﻿using System;
using System.IO;
using UnityEngine;

namespace stupid.solutions.Utils
{
    public static class Settings
    {
        internal static bool DrawLootItems = false;
        internal static bool DrawLootableContainers = false;
        internal static bool DrawExfiltrationPoints = false;

        internal static bool DrawPlayers = true;
        internal static bool DrawPlayerName = true;
        internal static bool DrawPlayerHealth = true;
        internal static bool DrawPlayerBox = false;
        internal static bool DrawPlayerLine = false;
        public static Color DeadBodyColor = Color.white;
        internal static float DrawLootItemsDistance = 100f;
        internal static float DrawLootableContainersDistance = 100f;
        internal static float DrawPlayersDistance = 200f;
        internal static int xOffset = -4;
        internal static int initialYOffset = -20;
        internal static int itemLineHeight = 20;
        internal static int lineWidth = -3;
        internal static int lineX = 15;
        
        internal static bool Aimbot = true;

        internal static float AimbotFOV = 5f;
        internal static float AimbotSmooth = 0f;
        internal static bool NoRecoil = true;
        internal static bool AimbotDrawFOV = true;
        internal static bool Instaheal = false;
        internal static float expamount = 20000;
        internal static bool EnableSkeleton = true;
        internal static bool Crosshair = true;
        internal static bool Flyhack = false;
        internal static bool Speedhack = false;
        internal static float Speed = 0.5f;
        internal static bool Infstamina = false;
        internal static bool Thermal = false;
        internal static bool Godmode = false;
        internal static bool XPBoost = false;
        internal static bool CheckVisible = false;
        internal static bool InstaSearch = true;
        internal static bool DrawInfo = true;
        internal static bool playerWeapon = false;
        internal static bool grenadeesp = true;
        internal static bool NoVisor = true;
        internal static bool streets = false;
        internal static bool drawaimpos = false;
        internal static bool TeleportItems = false;
        internal static bool PlayerInventoryESP = true;
        internal static bool weaponline = false;
        internal static bool flares = true;
        internal static bool ResetGodmode = false;
        internal static int Aimbone = 133;
        internal static bool changemaincamfov = false;
        internal static float maincamfov = 120f;
        internal static bool weaponchams = true;
        // item menu
        internal static bool superrare = true;
        internal static bool common = false;
        internal static bool kappa = false;
        internal static bool quest = false;
        internal static bool containername = false;
        internal static bool searchItem = false;
        internal static string searchitem = "Dogtag";
        internal static float commonitemdistance = 10f;
        internal static bool BodyESP = true;

        // radar
        internal static bool DrawRadar = true;
        internal static bool DrawPlayersr = true;
        internal static bool DrawScavs = true;
        internal static bool DrawWealth = true;
        internal static float RadarRange = 400f;
        internal static float RadarX = Screen.width - 300f;
        internal static float RadarY = Screen.height - 300f;
        internal static float RadarSize = 250f;
        internal static bool MagicBullet = false;
        internal static bool CompleteQuests = true;
        // test
        internal static bool SilentAim = false;
        internal static bool botaim = false;
        internal static bool BulletPenetration = true;
        internal static bool SilentTargetLines = true;
        internal static bool Chams = true;
        internal static bool instahit = true;
        internal static bool nojam = false;
        internal static bool firerate = false;
        internal static bool infammo = true;
        internal static bool Update = true;
        internal static bool HitMarkers = true;
        internal static bool tracers = true;
        // chams
        internal static float VisR = 255f;
        internal static float VisG = 255f;
        internal static float VisB = 255f;
        internal static float HidR = 255f;
        internal static float HidG = 98f;
        internal static float HidB = 0f;
        internal static bool Itemspawnmenu = false;
        internal static bool SpawnItem = false;
        internal static bool ThirdPerson = false;
        //keybinds
        public static KeyCode ClearCache = KeyCode.Delete;
        internal static KeyCode Skill = KeyCode.Keypad1;
        internal static KeyCode teleportitem = KeyCode.Keypad2;
        internal static KeyCode KillAll = KeyCode.Keypad7;
        internal static KeyCode UnlockDoors = KeyCode.Keypad4;
        internal static KeyCode AimbotKey = KeyCode.LeftControl;
        internal static KeyCode TPall = KeyCode.Keypad8;
        internal static KeyCode Instahealkey = KeyCode.Alpha5;
        private static readonly string SettingsFilePath = Path.Combine(Application.persistentDataPath, "settings.json");

        public static void LoadSettings()
        {
            if (File.Exists(SettingsFilePath))
            {
                try
                {
                    string json = File.ReadAllText(SettingsFilePath);
                    JsonUtility.FromJsonOverwrite(json, typeof(Settings));
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Failed to load settings: {ex.Message}");
                }
            }
            else
            {
                Debug.LogWarning("Settings file not found, using default settings.");
            }
        }

        public static void SaveSettings()
        {
            try
            {
                string json = JsonUtility.ToJson(typeof(Settings), true);
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to save settings: {ex.Message}");
            }
        }
    }
}