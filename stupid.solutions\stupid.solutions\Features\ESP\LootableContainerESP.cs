﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Comfort.Common;
using EFT;
using EFT.Interactive;
using stupid.solutions.Data;
using stupid.solutions.Utils;
using JsonType;
using UnityDiagnostics;
using UnityEngine;

namespace stupid.solutions.Features.ESP
{
    public class LootableContainerESP : MonoBehaviour
    {
        private static readonly float CacheLootItemsInterval = 10f;
        private float _nextLootContainerCacheTime;
        private List<GameLootContainer> _gameLootContainers;
        private static readonly Color LootableContainerColor = new Color(1f, 0.2f, 0.09f);

        public void Start()
        {
            _gameLootContainers = new List<GameLootContainer>();
        }

        public void Update()
        {
            if (!Settings.DrawLootableContainers)
                return;

            if (Time.time >= _nextLootContainerCacheTime)
            {
                if ((Main.GameWorld != null) && (Main.GameWorld.LootItems != null) && (Main.LocalPlayer != null))
                {
                    _gameLootContainers.Clear();

                    foreach (LootableContainer lootableContainer in FindObjectsOfType<LootableContainer>())
                    {
                        if (!GameUtils.IsLootableContainerValid(lootableContainer) || (Vector3.Distance(Main.MainCamera.transform.position, lootableContainer.transform.position) > Settings.DrawLootableContainersDistance))
                            continue;

                        _gameLootContainers.Add(new GameLootContainer(lootableContainer));
                    }
                    _nextLootContainerCacheTime = (Time.time + CacheLootItemsInterval);
                }
            }

            foreach (GameLootContainer gameLootContainer in _gameLootContainers)
                gameLootContainer.RecalculateDynamics();
        }


        void OnGUI()
        {
            if (!Settings.DrawLootableContainers)
                return;

            int xOffset = Settings.xOffset; 
            int initialYOffset = Settings.initialYOffset; 
            int itemLineHeight = Settings.itemLineHeight; 
            int lineWidth = Settings.lineWidth; 
            int lineX = Settings.lineX;

            
            GUIStyle headerStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.UpperLeft,
                fontSize = 12,
                normal = new GUIStyleState { textColor = Color.white } 
            };

           
            GUIStyle itemStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.UpperLeft,
                fontSize = 12
            };

            foreach (var gameLootContainer in _gameLootContainers)
            {
                if (!GameUtils.IsLootableContainerValid(gameLootContainer.LootableContainer) ||
                    !gameLootContainer.IsOnScreen ||
                    gameLootContainer.Distance > Settings.DrawLootableContainersDistance)
                    continue;

                Vector2 basePosition = new Vector2(gameLootContainer.ScreenPosition.x, gameLootContainer.ScreenPosition.y);

                
                Dictionary<string, int> itemCounts = new Dictionary<string, int>();

                foreach (var item in gameLootContainer.LootableContainer.ItemOwner.RootItem.GetAllItems())
                {
                    if (item == null)
                        continue;

                    string itemName = item.LocalizedName();

                    
                    bool IsSearchedItem = Settings.searchItem && Main.SearchedItem(itemName);
                    bool IsKappa = Settings.kappa && Main.IsKappa(itemName);
                    bool IsSuperrare = Settings.superrare && Main.IsSuperrare(itemName);

                    if (IsSearchedItem || IsKappa || IsSuperrare)
                    {
                        if (!itemCounts.ContainsKey(itemName))
                            itemCounts[itemName] = 0;
                        itemCounts[itemName]++;
                    }
                }

               
                if (itemCounts.Count > 0)
                {
                   
                    GUIStyle lineStyle = new GUIStyle();
                    Texture2D lineTexture = new Texture2D(1, 1);
                    lineTexture.SetPixel(0, 0, Color.yellow);
                    lineTexture.Apply();
                    GUI.DrawTexture(new Rect(basePosition.x - lineX, basePosition.y - initialYOffset, lineWidth, itemCounts.Count * itemLineHeight + itemLineHeight - 25), lineTexture);

                 
                    string containerLabel = $"Container - {gameLootContainer.FormattedDistance}";
                    GUI.Label(
                        new Rect(basePosition.x - lineX - xOffset - 8, basePosition.y - initialYOffset - itemLineHeight + 3, 200, itemLineHeight),
                        containerLabel,
                        headerStyle);

                   
                    int currentYOffset = initialYOffset + (itemLineHeight / 3); 

                    foreach (var itemName in itemCounts.Keys.OrderBy(name => GetCategoryPriority(name)))
                    {
                        int itemCount = itemCounts[itemName];
                        Color itemColor = GetItemColor(itemName);

                        
                        string itemCountText = itemCount > 1 ? $" ({itemCount})" : "";
                        string textToDraw = $"{itemName}{itemCountText}";

                        itemStyle.normal.textColor = itemColor;

                       
                        GUI.Label(
                            new Rect(basePosition.x - lineX - xOffset, basePosition.y - (currentYOffset - itemLineHeight) - 16, 200, itemLineHeight),
                            textToDraw,
                            itemStyle);

                        currentYOffset -= itemLineHeight; 
                    }
                }
            }
        }

        
        int GetCategoryPriority(string itemName)
        {
            if (Main.SearchedItem(itemName)) return 1;
            if (Main.IsKappa(itemName)) return 3;
            if (Main.IsSuperrare(itemName)) return 2;
            return 4;
            
        }

       
        Color GetItemColor(string itemName)
        {
            if (Settings.searchItem && Main.SearchedItem(itemName)) return Color.blue;
            if (Settings.kappa && Main.IsKappa(itemName)) return Color.green;
            if (Settings.superrare && Main.IsSuperrare(itemName)) return Color.red;
            return Color.white; 
        }
    }
}


