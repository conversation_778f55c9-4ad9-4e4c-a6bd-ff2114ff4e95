﻿using EFT;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using EFT.Interactive;
using stupid.solutions.Data;
using stupid.solutions.Features;
using stupid.solutions.Features.ESP;
using stupid.solutions.Utils;
using EFT.Hideout;
using EFT.UI.DragAndDrop;
using System.Collections;
using System.IO;
using EFT.InventoryLogic;
using System.Reflection;
using EFT.UI.Matchmaker;
using System.Security.Policy;
using static System.Collections.Specialized.BitVector32;
using RootMotion;
namespace stupid.solutions
{
    internal class locationsfixer : MonoBehaviour
    {
        private string _logFilePath;
        private TarkovApplication _tarkovApplication;
        private bool Streetspatched = false;

        void Start()
        {
            if (!Streetspatched)
            {
                if (Main.GameWorld != null)
                    return;
                fixlocation();
                EFT.UI.ConsoleScreen.Log("fixing streets...");
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.F4))
            {
                Settings.streets = !Settings.streets;                
            }
            if (!Settings.streets)
            {
                Streetspatched = false;
            }
            if (Settings.streets)
            {
                if (Main.GameWorld != null)
                {
                    Streetspatched = false;
                }

                if (!Streetspatched)
                {
                    if (Main.GameWorld != null)
                        return;

                    fixlocation();
                    EFT.UI.ConsoleScreen.Log("fixing streets...");
                }
            }
        }

       
        public void fixlocation()
        {
            _tarkovApplication = FindObjectOfType<TarkovApplication>();
            var clientbackend = _tarkovApplication.GetClientBackEndSession();
            var locations = clientbackend.LocationSettings.locations;
            if (locations == null)
                return;

            foreach (var location in locations.Values)
            {
                EFT.UI.ConsoleScreen.Log(location.Name);
                if (location.Name.Contains("Streets"))
                {
                    location.ForceOnlineRaidInPVE = false;
                    location.MaxBotPerZone = 0;
                    location.EscapeTimeLimit = 100000000;
                    EFT.UI.ConsoleScreen.Log("fixed streets");
                    Streetspatched = true;
                }
            }
        }
    }
}