﻿using EFT;
using stupid.solutions.Utils;
using stupid.solutions;
using UnityEngine;

class RayCast
{
    private static readonly LayerMask LayerMask = 1 << 12 | 1 << 16 | 1 << 18 | 1 << 31 | 1 << 22;
    private static RaycastHit _raycastHit;

    
    public static Vector3 BarrelRayCast(Player gamePlayer)
    {
        try
        {
            if (gamePlayer.Fireport == null)
                return Vector3.zero;

            Physics.Linecast(
                gamePlayer.Fireport.position,
                gamePlayer.Fireport.position - gamePlayer.Fireport.up * 1000f,
                out _raycastHit);

            return _raycastHit.point;
        }
        catch
        {
            return Vector3.zero;
        }
    }

    public static string BarrelRayCastTest(Player gamePlayer)
    {
        try
        {
            Physics.Linecast(gamePlayer.Fireport.position, gamePlayer.Fireport.position - gamePlayer.Fireport.up * 1000f, out _raycastHit);
            return _raycastHit.transform.name;
        }
        catch
        {
            return "Unkown";
        }
    }
}