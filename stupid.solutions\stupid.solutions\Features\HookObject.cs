﻿using System;
using System.Reflection;
using System.Runtime.CompilerServices;
using UnityEngine;
using EFT;
using stupid.solutions.Data;
using stupid.solutions.Features;
using stupid.solutions.Utils;
using EFT.HealthSystem;
using stupid.solutions.Features.ESP;
using EFT.InventoryLogic;
using System.Xml.Linq;
using static stupid.solutions.Utils.GameUtils;
using EFT.Quests;
using static UnityEngine.UI.GridLayoutGroup;
using System.Linq;
using System.Collections;
using System.Collections.Generic;

namespace stupid.solutions.Features
{
    public class HookObject
    {
        public object SilentAimHook(object ammo, Vector3 origin, Vector3 direction, int fireIndex, Player player, Item weapon, float speedFactor = 1f, int fragmentIndex = 0)
        {
            if (Main.LocalPlayer.HandsController.Item is Weapon)
            {
                if (Settings.SilentAim && Input.GetMouseButton(0))
                {
                    GamePlayer target = Aimbot.Target;
                    if (target != null && (player = Main.LocalPlayer))
                    {
                        Vector3 aimPosition = GameUtils.FinalVector(target.Player, Settings.Aimbone) + new Vector3(0f, 0.07246377f, 0f);
                        if (Main.LocalPlayer.WeaponRoot != null)
                        {
                            direction = (aimPosition - origin).normalized;
                            origin = Main.LocalPlayer.Fireport.position;
                        }
                    }
                }
            }

            Aimbot.CreateShotHook.Unhook();
            object[] parameters =
            {
                ammo,
                origin,
                direction,
                fireIndex,
                player,
                weapon,
                speedFactor,
                fragmentIndex
            };
            object result = Aimbot.CreateShotHook.OriginalMethod.Invoke(this, parameters);
            Aimbot.CreateShotHook.Hook();
            return result;
        }




    }
    public class BulletMovement
    {
        public bool BulletMovement_Hook(Vector3 prevPosition, Vector3 nextPosition)
        {
            
            Aimbot.BulletMovementHook.Unhook();
            bool ret = false;
            try
            {
                if (Input.GetMouseButton(0) && Settings.MagicBullet)
                {
                    GamePlayer target = Aimbot.Target;
                    if (target != null)
                    {              
                        Vector3 aimbonePosition = GameUtils.FinalVector(target.Player, Settings.Aimbone);            
                        Vector3 localPlayerPosition = Main.LocalPlayer.Fireport.position;       
                        Vector3 direction = (localPlayerPosition - aimbonePosition).normalized;
                        prevPosition = aimbonePosition + direction * 0.5f;
                        nextPosition = aimbonePosition; 
                    }
                }

                
                ret = (bool)Aimbot.BulletMovementHook.OriginalMethod.Invoke(this, new object[] { prevPosition, nextPosition });
               
            }
            catch (Exception e)
            {
              
                EFT.UI.ConsoleScreen.Log($"Exception in hook method:");
                EFT.UI.ConsoleScreen.Log($"Exception Type: {e.GetType()}");
                EFT.UI.ConsoleScreen.Log($"Exception Message: {e.Message}");
                EFT.UI.ConsoleScreen.Log($"Stack Trace: {e.StackTrace}");
            }
            finally
            {            
                Aimbot.BulletMovementHook.Hook();
            }

            return ret;
        }
    }

    public class QuestCompleterHookd
    {
        private static bool isQuestCompleterHooked = false;
        private static TestHook QuestCompleterHook;
        private static TestHook ResetNullableConditionsHook;
        private static EFT.Quests.Condition condition;
        public static void Init()
        {
            if (!isQuestCompleterHooked)
            {
                if (!Settings.CompleteQuests)
                    return;
                var conditionCounterManagerType = typeof(EFT.Quests.ConditionCounterManager);

                // Hook Test method
                var testMethod = conditionCounterManagerType.GetMethod("Test", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public);
                QuestCompleterHook = new TestHook();
                QuestCompleterHook.Init(testMethod, typeof(QuestCompleterHookd).GetMethod("Test_Hook", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public));
                QuestCompleterHook.Hook();

                // Hook ResetNullableConditions method
                var resetNullableConditionsMethod = conditionCounterManagerType.GetMethod("ResetNullableConditions", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public);
                ResetNullableConditionsHook = new TestHook();
                ResetNullableConditionsHook.Init(resetNullableConditionsMethod, typeof(QuestCompleterHookd).GetMethod("ResetNullableConditions_Hook", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public));
                ResetNullableConditionsHook.Hook();

                isQuestCompleterHooked = true;
                EFT.UI.ConsoleScreen.Log("Quest completer and ResetNullableConditions hooked!");
            }
        }

        public static void Test_Hook(object instance, int valueToAdd, params object[] checks)
        {
            if (!Settings.CompleteQuests)
                return;
            QuestCompleterHook.Unhook();
            try
            {
                var countersField = instance.GetType().GetField("_counters", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var counters = countersField.GetValue(instance) as IEnumerable;

                if (counters == null)
                {
                    EFT.UI.ConsoleScreen.Log("Counters list is null");
                    return;
                }

                foreach (var counter in counters)
                {
                    var templateField = counter.GetType().GetField("Template", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                    var template = templateField.GetValue(counter);

                    if (template is ConditionCounterCreator creator)
                    {
                        var valueField = counter.GetType().GetProperty("Value", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                        valueField.SetValue(counter, (int)Math.Ceiling((double)creator.value));
                    }
                }
            }
            catch (Exception e)
            {
                EFT.UI.ConsoleScreen.Log($"Exception in quest completer hook: {e.Message}");
            }
            finally
            {
                QuestCompleterHook.Hook();
            }
        }

        public static void ResetNullableConditions_Hook(object instance)
        {
            // Do nothing
        }
    }
}