﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace stupid.solutions.enums
{
    internal enum HumanBones
    {
        None,
        <PERSON><PERSON><PERSON><PERSON><PERSON>er,
        Mesh,
        Vest_0,
        Vest_1,
        backpack,
        backpack_0,
        backpack_1,
        backpack_2,
        razgruz,
        razgruz_0,
        razgruz_1,
        razgruz_2,
        Root_Joint,
        HumanPelvis,
        HumanLThigh1,
        HumanLThigh2,
        HumanLCalf,
        HumanLFoot,
        HumanLToe,
        HumanRThigh1,
        HumanRThigh2,
        HumanRCalf,
        HumanRFoot,
        HumanRT<PERSON>,
        Bear_Feet,
        USEC_Feet,
        BEAR_feet_1,
        weapon_holster_pistol,
        HumanSpine1,
        HumanGear1,
        HumanGear2,
        HumanGear3,
        HumanGear4,
        HumanGear4_1,
        HumanGear5,
        HumanSpine2,
        HumanSpine3,
        IK_S_LForearm1,
        IK_S_LForearm2,
        IK_S_LForearm3,
        IK_S_LPalm,
        IK_S_LDigit11,
        IK_S_LDigit12,
        IK_S_LDigit13,
        I<PERSON>_S_LDigit21,
        IK_S_LDigit22,
        <PERSON>K_S_LDigit23,
        IK_S_LDigit31,
        IK_S_LDigit32,
        IK_S_LDigit33,
        IK_S_LDigit41,
        IK_S_LDigit42,
        IK_S_LDigit43,
        IK_S_LDigit51,
        IK_S_LDigit52,
        IK_S_LDigit53,
        XYZ,
        LCollarbone_anim,
        RCollarbone_anim,
        RCollarbone_anim_XYZ,
        Weapon_root_3rd_anim,
        XYZ_1,
        Bend_Goal_Left,
        Bend_Goal_Right,
        Bend_Goal_Right_XYZ_1,
        HumanRibcage,
        IK_LForearm1,
        IK_LForearm2,
        IK_LForearm3,
        IK_LPalm,
        IK_LDigit11,
        IK_LDigit12,
        IK_LDigit13,
        IK_LDigit21,
        IK_LDigit22,
        IK_LDigit23,
        IK_LDigit31,
        IK_LDigit32,
        IK_LDigit33,
        IK_LDigit41,
        IK_LDigit42,
        IK_LDigit43,
        IK_LDigit51,
        IK_LDigit52,
        IK_LDigit53,
        Camera_animated,
        CameraContainer,
        Cam,
        HumanLCollarbone,
        HumanLUpperarm,
        HumanLForearm1,
        HumanLForearm2,
        HumanLForearm3,
        HumanLPalm,
        HumanLDigit11,
        HumanLDigit12,
        HumanLDigit13,
        HumanLDigit21,
        HumanLDigit22,
        HumanLDigit23,
        HumanLDigit31,
        HumanLDigit32,
        HumanLDigit33,
        HumanLDigit41,
        HumanLDigit42,
        HumanLDigit43,
        HumanLDigit51,
        HumanLDigit52,
        HumanLDigit53,
        HumanRCollarbone,
        HumanRUpperarm,
        HumanRForearm1,
        HumanRForearm2,
        HumanRForearm3,
        HumanRPalm,
        HumanRDigit11,
        HumanRDigit12,
        HumanRDigit13,
        HumanRDigit21,
        HumanRDigit22,
        HumanRDigit23,
        HumanRDigit31,
        HumanRDigit32,
        HumanRDigit33,
        HumanRDigit41,
        HumanRDigit42,
        HumanRDigit43,
        HumanRDigit51,
        HumanRDigit52,
        HumanRDigit53,
        Weapon_root,
        HumanNeck,
        HumanHead,
        HumanBackpack,
        weapon_holster,
        weapon_holster1,
        Camera_animated_3rd
    }
}
