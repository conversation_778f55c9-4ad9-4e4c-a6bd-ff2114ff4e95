﻿
using EFT.InventoryLogic;
using stupid.solutions.Features;
using UnityEngine;

namespace stupid.solutions
{
    public class Loader
    {
        public static GameObject HookObject;

        public static void Load()
        {
            HookObject = new GameObject();
            HookObject.AddComponent<Main>();
            Object.DontDestroyOnLoad(HookObject);
        }
        
       
        
    }

}
