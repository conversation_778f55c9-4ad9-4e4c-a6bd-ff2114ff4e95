﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using Comfort.Common;
using EFT;
using EFT.Interactive;
using EFT.InventoryLogic;
using stupid.solutions.Data;
using stupid.solutions.Utils;
using JetBrains.Annotations;
using JsonType;
using UnityEngine;

namespace stupid.solutions.Features.ESP
{
    public class ItemESP : MonoBehaviour
    {
        private static readonly float CacheLootItemsInterval = 1f;
        private float _nextLootItemCacheTime;

        //private static readonly Color SpecialColor = new Color(1f, 0.2f, 0.09f);
        private static readonly Color QuestColor = Color.yellow;
        private static readonly Color CommonColor = Color.white;
        private static readonly Color RareColor = Color.blue;
        private static readonly Color SuperRareColor = Color.red;
        public static Player LocalPlayer;
        private List<GameLootItem> _gameLootItems = new List<GameLootItem>();
        private List<WishlistItem> _wishlistItems = new List<WishlistItem>();
        private Stopwatch _stopwatch = new Stopwatch();
        
        public void Update()
        {
            if (!Settings.DrawLootItems)
                return;

            if (Time.time >= _nextLootItemCacheTime)
            {
                if ((Main.GameWorld != null) && (Main.GameWorld.LootItems != null))
                {
                    _gameLootItems.Clear();

                    for (int i = 0; i < Main.GameWorld.LootItems.Count; i++)
                    {
                        LootItem lootItem = Main.GameWorld.LootItems.GetByIndex(i);

                        if (!GameUtils.IsLootItemValid(lootItem) || (Vector3.Distance(Main.MainCamera.transform.position, lootItem.transform.position) > Settings.DrawLootItemsDistance))
                            continue;

                        _gameLootItems.Add(new GameLootItem(lootItem));
                    }

                    _nextLootItemCacheTime = (Time.time + CacheLootItemsInterval);
                }
            }

            foreach (GameLootItem gameLootItem in _gameLootItems)
                gameLootItem.RecalculateDynamics();

        }

        private void OnGUI()
        {
            if (Settings.DrawLootItems)
            {
                foreach (var gameLootItem in _gameLootItems)
                {
                    if (!GameUtils.IsLootItemValid(gameLootItem.LootItem) || !gameLootItem.IsOnScreen || gameLootItem.Distance > Settings.DrawLootItemsDistance)
                        continue;
                    string lootItemName = $"{gameLootItem.LootItem.Item.ShortName.Localized()} [{gameLootItem.FormattedDistance}]";

                    
                    if (Settings.quest)
                    {
                        if (gameLootItem.LootItem.Item.Template.QuestItem)
                            Render.DrawString(new Vector2(gameLootItem.ScreenPosition.x - 50f, gameLootItem.ScreenPosition.y), lootItemName, QuestColor);
                    }
                  
                    bool IsSupperrare = Main.IsSuperrare(gameLootItem.LootItem.Item.LocalizedName());
                    if (IsSupperrare)
                    {
                        if (Settings.superrare)
                            Render.DrawString(new Vector2(gameLootItem.ScreenPosition.x - 50f, gameLootItem.ScreenPosition.y), gameLootItem.LootItem.Item.LocalizedShortName() + " - " + gameLootItem.FormattedDistance, Color.red);                        

                    }
                    bool IsKappa = Main.IsKappa(gameLootItem.LootItem.Item.LocalizedName());
                    if (Settings.kappa)
                    {                        
                        if (IsKappa)
                        {
                            Render.DrawString(new Vector2(gameLootItem.ScreenPosition.x - 50f, gameLootItem.ScreenPosition.y), gameLootItem.LootItem.Item.LocalizedShortName() + " - " + gameLootItem.FormattedDistance, Color.green);
                        }
                    }
                    bool IsSearchedItem = Main.SearchedItem(gameLootItem.LootItem.Item.LocalizedName());
                    if (Settings.searchItem)
                    {                       
                        if (IsSearchedItem)
                            Render.DrawString(new Vector2(gameLootItem.ScreenPosition.x - 50f, gameLootItem.ScreenPosition.y), gameLootItem.LootItem.Item.LocalizedShortName() + " - " + gameLootItem.FormattedDistance, Color.blue);
                    }
                    bool IsDeadBody = Main.Isdeadbody(gameLootItem.LootItem.Item.LocalizedName());
                    if (Settings.common)
                    {
                        if (!IsSupperrare && !IsSearchedItem && !IsKappa && !IsDeadBody)
                        {
                            if(gameLootItem.Distance < Settings.commonitemdistance)
                            {
                                Render.DrawString(new Vector2(gameLootItem.ScreenPosition.x - 50f, gameLootItem.ScreenPosition.y), gameLootItem.LootItem.Item.LocalizedShortName() + " - " + gameLootItem.FormattedDistance, Color.white);
                            }
                            
                        }




                    }
                   
                    
                }
                


            }
            if (Settings.weaponchams)
            {
                weaponChams();
            }
            
        }
        private void weaponChams()
        {
            if (Main.GameWorld == null)
                return;

            if (Settings.weaponchams)
            {
                var component = Main.LocalPlayer.GetComponentsInChildren<Renderer>();

                foreach (var render in component)
                {
                    var material = render.material;
                    if (!material)
                        continue;
                    material.shader = Main.bundle.LoadAsset<Shader>("forcefield.shader");
                    material.SetColor("_Color", new Color32(0, 255, 229, 155));
                    //material.SetColor("_ColorBehind", new Color32(0, 255, 229, 122));
                    //var name = material.name;

                    //EFT.UI.ConsoleScreen.Log($"{name}");

                    //if (name.Contains("weapon") || name.Contains("ammo") || name.Contains("grip") || name.Contains("mount") || name.Contains("item") || name.Contains("mag") || name.Contains("tactical") || name.Contains("scope") || name.Contains("barrel") || name.Contains("patron") || name.Contains("muzzle") || name.Contains("cover") || name.Contains("clip"))
                    //{
                    //material.shader = Main.bundle.LoadAsset<Shader>("forcefield.shader");
                    // material.SetColor("_ColorVisible", new Color32(0, 255, 229, 255));
                    // material.SetColor("_ColorBehind", new Color32(0, 255, 229, 255));
                    //}

                }
            }
        }
        
    }

}
