﻿using EFT.Interactive;
using System;
using UnityEngine;

namespace stupid.solutions.Utils
{
    class GameCorpse
    {
        public Corpse Corpse { get; }
        public Vector3 ScreenPosition => _screenPosition;

        public bool IsOnScreen { get; private set; }

        public float Distance { get; private set; }

        public string FormattedDistance => $"{Math.Round(Distance)}m";

        private Vector3 _screenPosition;

        public GameCorpse(Corpse corpse)
        {
            Corpse = corpse ?? throw new ArgumentNullException(nameof(corpse));
            _screenPosition = default;
            Distance = 0f;
        }

        public void RecalculateDynamics()
        {
            if (!IsBodyValid(Corpse))
                return;

            _screenPosition = GameUtils.WorldPointToScreenPoint(Corpse.transform.position);
            IsOnScreen = GameUtils.IsScreenPointVisible(_screenPosition);
            Distance = Vector3.Distance(Main.MainCamera.transform.position, Corpse.transform.position);
        }
        public static bool IsBodyValid(Corpse corpse)
        {
            return corpse != null;
        }
    }
}
