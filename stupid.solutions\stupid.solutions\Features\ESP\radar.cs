﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EFT.UI;
using stupid.solutions.Utils;
using UnityEngine;
using EFT.InventoryLogic;
using EFT;
namespace stupid.solutions.Features.ESP
{


    class Radar : MonoBehaviour
    {

        private static readonly Color _playerColor = Color.green;
        private static readonly Color _botColor = Color.yellow;
        private static readonly Color _healthColor = Color.green;
        private static readonly Color _bossColor = Color.red;
        private void Update()
        {
            try
            {
                if ((Main.GameWorld != null) && Settings.DrawRadar)
                {

                }
            }
            catch { }

        }
        private void OnGUI()
        {
            try
            {
                if (Settings.DrawRadar)
                {
                    Render.BoxRect(new Rect(Settings.RadarX + Settings.RadarSize / 2f - 3f, Settings.RadarY + Settings.RadarSize / 2f - 3f, 6f, 6f), Color.white);

                    foreach (var gamePlayer in Main.GamePlayers)
                    {
                        if ((!gamePlayer.IsAI || Settings.DrawScavs) && (gamePlayer.IsAI || Settings.DrawPlayers))
                        {
                            float y = Main.LocalPlayer.Transform.position.x - gamePlayer.Player.Transform.position.x;
                            float x = Main.LocalPlayer.Transform.position.z - gamePlayer.Player.Transform.position.z;

                            float atan = Mathf.Atan2(y, x) * 57.29578f - 270 - Main.LocalPlayer.Transform.eulerAngles.y;

                            float num3 = gamePlayer.Distance * Mathf.Cos(atan * 0.0174532924f);
                            float num4 = gamePlayer.Distance * Mathf.Sin(atan * 0.0174532924f);

                            num3 = num3 * (Settings.RadarSize / Settings.RadarRange) / 2f;
                            num4 = num4 * (Settings.RadarSize / Settings.RadarRange) / 2f;
                            Color playerColor = ((gamePlayer.IsAI) ? _botColor : _playerColor);
                            if (gamePlayer.Distance <= Settings.RadarRange)
                            {
                                if (gamePlayer.Player.Profile.Info.Settings.IsBoss())
                                {
                                    bool IsBoss = Main.IsBossByName(gamePlayer.Player.Profile.Info.Nickname.Localized());
                                    bool IsRaider = Main.IsRussian(gamePlayer.Player.Profile.Info.Nickname.Localized());                                   
                                    bool IsRogue = Main.IsRogue(gamePlayer.Player.Profile.Info.Nickname.Localized());
                                   
                                    if (IsBoss)
                                    {

                                        playerColor = _bossColor;
                                    }


                                    else if (IsRaider)
                                    {

                                        playerColor = Color.magenta;
                                    }
                                    else if (IsRogue)
                                    {
                                        playerColor = Color.grey;
                                    }
                                    else
                                    {
                                        playerColor = _playerColor;

                                    }


                                }
                                else if (gamePlayer.IsAI)
                                {

                                    playerColor = _botColor;
                                }
                                else
                                {

                                    playerColor = _playerColor;
                                }
                                Render.BoxRect(new Rect(Settings.RadarX + Settings.RadarSize / 2f + num3 - 3f, Settings.RadarY + Settings.RadarSize / 2f + num4 - 3f, 6f, 6f), playerColor);
                            }
                        }
                    }
                    Render.DrawCornerBox(new Vector2(Settings.RadarX + (Settings.RadarSize / 2), Settings.RadarY), Settings.RadarSize, Settings.RadarSize, Color.white, true);
                    Render.DrawRadarBackground(new Rect(Settings.RadarX, Settings.RadarY, Settings.RadarSize, Settings.RadarSize));
                }

            }
            catch { }
        }

    }
}
