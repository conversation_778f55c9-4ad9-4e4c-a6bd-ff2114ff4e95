﻿using System;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using UnityEngine;

namespace stupid.solutions
{
    public class TestHook
    {
        public MethodInfo OriginalMethod { get; private set; }
        public MethodInfo HookMethod { get; private set; }

        private byte[] original;
        private const uint HOOK_SIZE_X64 = 12U;
        private const uint HOOK_SIZE_X86 = 7U;

        public TestHook()
        {
            original = null;
            OriginalMethod = HookMethod = null;
        }

        public TestHook(MethodInfo orig, MethodInfo hook)
        {
            original = null;
            Init(orig, hook);
        }

        public MethodInfo GetMethodByName(Type typeOrig, string nameOrig)
        {
            return typeOrig.GetMethod(nameOrig);
        }

        public TestHook(Type typeOrig, string nameOrig, Type typeHook, string nameHook)
        {
            original = null;
            Init(GetMethodByName(typeOrig, nameOrig), GetMethodByName(typeHook, nameHook));
        }

        public void Init(MethodInfo orig, MethodInfo hook)
        {
            if (orig == null || hook == null)
            {
                Logger.Log("Both original and hook methods must be valid.");
                throw new ArgumentException("Both original and hook need to be valid methods");
            }
            RuntimeHelpers.PrepareMethod(orig.MethodHandle);
            RuntimeHelpers.PrepareMethod(hook.MethodHandle);
            OriginalMethod = orig;
            HookMethod = hook;
            Logger.Log($"TestHook initialized: OriginalMethod = {orig.Name}, HookMethod = {hook.Name}");
        }

        public unsafe void Hook()
        {
            if (OriginalMethod == null || HookMethod == null)
            {
                Logger.Log("Hook method cannot be null.");
                throw new ArgumentException("Hook has to be properly Init'd before use");
            }

            try
            {
                Logger.Log("Hook() called");

                IntPtr functionPointer = OriginalMethod.MethodHandle.GetFunctionPointer();
                IntPtr functionPointer2 = HookMethod.MethodHandle.GetFunctionPointer();

                if (IntPtr.Size == 8)
                {
                    original = new byte[HOOK_SIZE_X64];
                    uint newProtect;
                    if (Import.VirtualProtect(functionPointer, HOOK_SIZE_X64, 0x40, out newProtect))
                    {
                        byte* ptr = (byte*)functionPointer;
                        for (int i = 0; i < HOOK_SIZE_X64; i++)
                        {
                            original[i] = ptr[i];
                        }
                        ptr[0] = 0x48;
                        ptr[1] = 0xB8;
                        *(IntPtr*)(ptr + 2) = functionPointer2;
                        ptr[10] = 0xFF;
                        ptr[11] = 0xE0;
                        Logger.Log("Hook installed successfully (x64).");
                    }
                    else
                    {
                        Logger.Log("Failed to change memory protection for hook (x64).");
                    }
                }
                else
                {
                    original = new byte[HOOK_SIZE_X86];
                    uint newProtect;
                    if (Import.VirtualProtect(functionPointer, HOOK_SIZE_X86, 0x40, out newProtect))
                    {
                        byte* ptr2 = (byte*)functionPointer;
                        for (int i = 0; i < HOOK_SIZE_X86; i++)
                        {
                            original[i] = ptr2[i];
                        }
                        ptr2[0] = 0xB8;
                        *(IntPtr*)(ptr2 + 1) = functionPointer2;
                        ptr2[5] = 0xFF;
                        ptr2[6] = 0xE0;
                        Logger.Log("Hook installed successfully (x86).");
                    }
                    else
                    {
                        Logger.Log("Failed to change memory protection for hook (x86).");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Exception in Hook(): {ex.Message}");
            }
        }

        public unsafe void Unhook()
        {
            if (original == null)
            {
                Logger.Log("No hook to remove.");
                return;
            }

            try
            {
                Logger.Log("Unhook() called");

                IntPtr functionPointer = OriginalMethod.MethodHandle.GetFunctionPointer();
                uint num = (uint)original.Length;
                uint newProtect;
                if (Import.VirtualProtect(functionPointer, num, 0x40, out newProtect))
                {
                    byte* ptr = (byte*)functionPointer;
                    for (int i = 0; i < num; i++)
                    {
                        ptr[i] = original[i];
                    }
                    Logger.Log("Hook removed successfully.");
                }
                else
                {
                    Logger.Log("Failed to change memory protection for unhook.");
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Exception in Unhook(): {ex.Message}");
            }
            finally
            {
                original = null;
            }
        }

        internal class Import
        {
            [DllImport("kernel32.dll", SetLastError = true)]
            internal static extern bool VirtualProtect(IntPtr address, uint size, uint newProtect, out uint oldProtect);
        }
    }
}