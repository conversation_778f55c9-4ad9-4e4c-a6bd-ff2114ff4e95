﻿using System;
using System.Linq;
using System.Security.Policy;
using Diz.Skinning;
using EFT;
using EFT.Interactive;
using EFT.InventoryLogic;
using UnityEngine;

namespace stupid.solutions.Utils
{

    public static class GameUtils
    {
        
        public static float Map(float value, float sourceFrom, float sourceTo, float destinationFrom, float destinationTo)
        {
            return (value - sourceFrom) / (sourceTo - sourceFrom) * (destinationTo - destinationFrom) + destinationFrom;
        }
        public static bool IsPlayerValid(Player player)
        {
            return player != null && player.Transform != null && player.PlayerBones != null && player.PlayerBones.transform != null;
        }
        public static bool IsExfiltrationPointValid(ExfiltrationPoint lootItem)
        {
            return lootItem != null;
        }
        public static bool IsLootItemValid(LootItem lootItem)
        {
            return lootItem != null && lootItem.Item != null && lootItem.Item.Template != null;
        }

        public static bool IsLootableContainerValid(LootableContainer lootableContainer)
        {
            return lootableContainer != null && lootableContainer.Template != null;
        }

        public static bool IsPlayerAlive(Player player)
        {
            if (!IsPlayerValid(player))
                return false;

            if (player.HealthController == null)
                return false;

            return player.HealthController.IsAlive;
        }
        public static Vector2 ScreenCenter = new Vector2(Screen.width / 2f, Screen.height / 2f);
        public static bool IsCorpseValid(Corpse corpse)
        {
            return corpse != null;
        }
        public static bool IsInventoryItemValid(Item item)
        {
            return item != null && item.Template != null;
        }
        public static bool IsFriend(Player player)
        {
            return Main.LocalPlayer.Profile.Info.GroupId == player.Profile.Info.GroupId && player.Profile.Info.GroupId != "0" && player.Profile.Info.GroupId != "" && player.Profile.Info.GroupId != null;
        }
        public static Vector3 WorldPointToScreenPoint(Vector3 worldPoint)
        {
            var screenPoint = Main.MainCamera.WorldToScreenPoint(worldPoint);
            var scale = Screen.height / (float)Main.MainCamera.scaledPixelHeight;
            screenPoint.y = Screen.height - screenPoint.y * scale;
            screenPoint.x *= scale;
            return screenPoint;
        }

        public static bool IsScreenPointVisible(Vector3 screenPoint)
        {
            return screenPoint.z > 0.01f && screenPoint.x > -5f && screenPoint.y > -5f && screenPoint.x < Screen.width && screenPoint.y < Screen.height;
        }

        public static Vector3 GetBonePosByID(Player player, int id)
        {
            Vector3 result;
            try
            {
                result = SkeletonBonePos(player.PlayerBones.AnimatedTransform.Original.gameObject.GetComponent<PlayerBody>().SkeletonRootJoint, id);
            }
            catch (Exception)
            {
                result = Vector3.zero;
            }
            return result;
        }

        public static Vector3 SkeletonBonePos(Skeleton skeleton, int id)
        {
            return skeleton.Bones.ElementAt(id).Value.position;
        }
        public static Vector3 FinalVector(Player player, int bone)
        {
            try
            {
                return player.PlayerBody.SkeletonRootJoint.Bones.ElementAt(bone).Value.position;
            }
            catch { return Vector3.zero; }
        }
        public class Tracer
        {
            public Vector3 StartPos;
            public Vector3 EndPos;
            public float Time;
        }

        public class HitMarker
        {
            public Vector3 HitPos;
            public float Time;
        }

    }

}