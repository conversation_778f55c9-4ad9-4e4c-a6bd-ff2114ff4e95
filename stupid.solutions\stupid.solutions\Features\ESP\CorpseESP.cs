﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace stupid.solutions.Features.ESP
{
    using System.Linq;
    using EFT;
    using EFT.Interactive;
    using stupid.solutions.Data;
    using stupid.solutions.Utils;
    using UnityEngine;

    class CorpseEsp : MonoBehaviour
    {
        private Texture2D stex;
        internal static List<GameCorpse> Corpses = new List<GameCorpse>();
        private float _nextBodyCacheTime;
        private static readonly float CacheLootItemsInterval = 10f;
        void Start()
        {

            stex = new Texture2D(1, 1);
            stex.SetPixel(0, 0, Color.blue);
            stex.Apply();
        }
        public void Update()
        {
            if (!Settings.BodyESP)
                return;

            if (Time.time >= _nextBodyCacheTime)
            {
                if ((Main.GameWorld != null) && (Main.GameWorld.ObservedPlayersCorpses != null))
                {
                    Corpses.Clear();

                    foreach (Corpse corpse in FindObjectsOfType<Corpse>())
                    {
                        if (!GameUtils.IsCorpseValid(corpse) || (Vector3.Distance(Main.MainCamera.transform.position, corpse.transform.position) > Settings.DrawLootableContainersDistance))
                            continue;

                        Corpses.Add(new GameCorpse(corpse));
                    }
                    _nextBodyCacheTime = (Time.time + CacheLootItemsInterval);
                }
            }

            foreach (GameCorpse corpse in Corpses)
                corpse.RecalculateDynamics();
        }
        void OnGUI()
        {
            if (!Settings.BodyESP)
                return;

            int xOffset = Settings.xOffset;
            int initialYOffset = Settings.initialYOffset;
            int itemLineHeight = Settings.itemLineHeight;
            int lineWidth = Settings.lineWidth;
            int lineX = Settings.lineX;


            GUIStyle headerStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.UpperLeft,
                fontSize = 12,
                normal = new GUIStyleState { textColor = Color.cyan }
            };


            GUIStyle itemStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.UpperLeft,
                fontSize = 12
            };

            foreach (var corpse in Corpses)
            {
                if (!corpse.IsOnScreen || corpse.Distance > Settings.DrawLootableContainersDistance)
                    continue;

                var items = corpse.Corpse.ItemOwner.RootItem;


                Dictionary<string, int> itemCounts = new Dictionary<string, int>();

                foreach (var item in items.GetAllItems())
                {
                    string itemName = item.LocalizedName();

                    bool IsSearchedItem = Main.SearchedItem(itemName);
                    bool IsKappa = Main.IsKappa(itemName);
                    bool IsSuperrare = Main.IsSuperrare(itemName);

                    if ((Settings.searchItem && IsSearchedItem) ||
                        (Settings.kappa && IsKappa) ||
                        (Settings.superrare && IsSuperrare))
                    {
                        if (!itemCounts.ContainsKey(itemName))
                            itemCounts[itemName] = 0;
                        itemCounts[itemName]++;
                    }
                }


                if (itemCounts.Count > 0)
                {

                    GUIStyle lineStyle = new GUIStyle();
                    Texture2D lineTexture = new Texture2D(1, 1);
                    lineTexture.SetPixel(0, 0, Color.yellow);
                    lineTexture.Apply();
                    GUI.DrawTexture(new Rect(corpse.ScreenPosition.x - lineX, corpse.ScreenPosition.y - initialYOffset, lineWidth, itemCounts.Count * itemLineHeight + itemLineHeight - 25), lineTexture);

                    string corpseLabel = $"Dead Body - {corpse.FormattedDistance}";
                    GUI.Label(
                        new Rect(corpse.ScreenPosition.x - lineX - xOffset - 8, corpse.ScreenPosition.y - initialYOffset - itemLineHeight + 3, 200, itemLineHeight),
                        corpseLabel,
                        headerStyle);


                    int currentYOffset = initialYOffset + (itemLineHeight / 3);

                    foreach (var itemName in itemCounts.Keys.OrderBy(name => GetCategoryPriority(name)))
                    {
                        int itemCount = itemCounts[itemName];
                        Color itemColor = GetItemColor(itemName);


                        string itemCountText = itemCount > 1 ? $" ({itemCount})" : "";
                        string textToDraw = $"{itemName}{itemCountText}";

                        itemStyle.normal.textColor = itemColor;


                        GUI.Label(
                            new Rect(corpse.ScreenPosition.x - lineX - xOffset, corpse.ScreenPosition.y - (currentYOffset - itemLineHeight) - 16, 200, itemLineHeight),
                            textToDraw,
                            itemStyle);

                        currentYOffset -= itemLineHeight;
                    }
                }
            }
        }


        int GetCategoryPriority(string itemName)
        {
            if (Main.SearchedItem(itemName)) return 1;
            if (Main.IsKappa(itemName)) return 3;
            if (Main.IsSuperrare(itemName)) return 2;
            return 4;
        }


        Color GetItemColor(string itemName)
        {
            if (Settings.searchItem && Main.SearchedItem(itemName)) return Color.blue;
            if (Settings.kappa && Main.IsKappa(itemName)) return Color.green;
            if (Settings.superrare && Main.IsSuperrare(itemName)) return Color.red;
            return Color.white;
        }
    }
}