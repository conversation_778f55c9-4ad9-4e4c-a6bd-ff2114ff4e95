﻿using System;
using System.Diagnostics.Eventing.Reader;
using System.Collections.Generic;
using System.Linq;
using BSG.CameraEffects;
using EFT;
using EFT.Ballistics;
using EFT.InventoryLogic;
using EFT.UI.BattleTimer;
using stupid.solutions.Utils;
using RootMotion.FinalIK;
using stupid.solutions.Features;
using UnityEngine;
using EFT.Animations;
using stupid.solutions.Data;
using static RootMotion.FinalIK.GenericPoser;
using System.Reflection;
using stupid.solutions;

namespace stupid.solutions.Features
{
    class Aimbot : MonoBehaviour
    {
        public static bool NotHooked = true;
        private bool isBulletMovementHooked = false;
        public static TestHook CreateShotHook;
        public static TestHook BulletMovementHook;
        public static GamePlayer Target;
        public static List<GameUtils.Tracer> TracerList = new List<GameUtils.Tracer>();
        public static List<GameUtils.HitMarker> HitList = new List<GameUtils.HitMarker>();

        public void Update()
        {
            if (Main.GameWorld != null)
            {
                if (Settings.NoRecoil)
                    NoRecoil();

                if (Settings.Aimbot)
                    Aim();
                if (Settings.SilentAim)
                    GetTarget();
                if (Settings.tracers)
                {
                    if (!isBulletMovementHooked && Main.LocalPlayer)
                    {
                        var BulletMovementMethod = Find("\uef71").GetMethod("\ue013", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic); // to find the correct class look for timesinceshot and add the corresponding unicode instead of uef71 incase of update
                        BulletMovementHook = new TestHook();
                        BulletMovementHook.Init(BulletMovementMethod, typeof(BulletMovement).GetMethod("BulletMovement_Hook"));
                        BulletMovementHook.Hook();
                        isBulletMovementHooked = true;
                        EFT.UI.ConsoleScreen.Log("Bulletmovement hooked!");
                    }
                }

                FixedUpdate();
            }
        }
        public static Type Find(string type)
        {
            var assembly = AppDomain.CurrentDomain.GetAssemblies()
                                .FirstOrDefault(a => a.GetName().Name == "Assembly-CSharp");
            foreach (Type type2 in assembly.GetTypes())
            {
                if (type == type2.Name || type == type2.FullName)
                {
                    return type2;
                }
            }
            return null;
        }

        private void FixedUpdate()
        {
            try
            {
                if (Main.GameWorld != null && Main.LocalPlayer.HandsController.Item is Weapon)
                {
                    if (Settings.SilentAim && NotHooked && Input.GetMouseButton(0))
                    {
                        CreateShotHook = new TestHook();
                        CreateShotHook.Init(typeof(BallisticsCalculator).GetMethod("CreateShot"), typeof(HookObject).GetMethod("SilentAimHook"));
                        CreateShotHook.Hook();
                        NotHooked = false;
                    }

                    Target = GetTarget();
                }
            }
            catch
            {
            }
        }

        public static bool is_visible(GameObject obj, Vector3 Pos, Vector3 Position, out RaycastHit raycastHit)
        {
            return Physics.Linecast(Pos, Position, out raycastHit, -**********) && raycastHit.collider && raycastHit.collider.gameObject.transform.root.gameObject == obj.transform.root.gameObject;
        }

        private GamePlayer GetTarget()
        {
            Dictionary<GamePlayer, int> dictionary = new Dictionary<GamePlayer, int>();

            foreach (var player in Main.GamePlayers)
            {
                if (player == null || player.Player.IsYourPlayer || !player.Player.HealthController.IsAlive)
                    continue;

                Vector3 destination = GameUtils.GetBonePosByID(player.Player, Settings.Aimbone);

                float distance = Vector3.Distance(Main.MainCamera.transform.position, player.Player.Transform.position);

                Vector3 vector2 = player.Player.Transform.position - Main.MainCamera.transform.position;

                if (player.Distance <= Settings.DrawPlayersDistance && CaulculateInFov(destination) <= Settings.AimbotFOV && Vector3.Dot(Main.MainCamera.transform.TransformDirection(Vector3.forward), vector2) > 0f)
                {
                    RaycastHit hit;
                    if (!Settings.CheckVisible || is_visible(player.Player.gameObject, Main.LocalPlayer.Fireport.position, destination, out hit))
                    {
                        dictionary.Add(player, (int)player.DistanceFromCenter);
                    }
                }
            }
            if (dictionary.Count > 0)
            {
                dictionary = (from pair in dictionary orderby pair.Value select pair).ToDictionary(pair => pair.Key, pair => pair.Value);
                return dictionary.Keys.First();
            }

            // No target found
            return null;
        }

        private void Aim()
        {
            if (Input.GetKey(Settings.AimbotKey))
            {
                Vector3 target = Vector3.zero;
                float distanceOfTarget = 9999f;

                foreach (var player in Main.GamePlayers)
                {
                    if (player == null || player.Player.IsYourPlayer || !player.Player.HealthController.IsAlive)
                        continue;

                    Vector3 destination = GameUtils.GetBonePosByID(player.Player, Settings.Aimbone); //neck

                    float distance = Vector3.Distance(Main.MainCamera.transform.position, player.Player.Transform.position);

                    Weapon weapon = Main.LocalPlayerWeapon;
                    if (destination != Vector3.zero && CaulculateInFov(destination) <= Settings.AimbotFOV)
                    {
                        RaycastHit hit;
                        if (!Settings.CheckVisible || is_visible(player.Player.gameObject, Main.LocalPlayer.Fireport.position, destination, out hit))
                        {
                            if (distanceOfTarget > distance)
                            {
                                distanceOfTarget = distance;
                                if (weapon != null)
                                {
                                    if (Main.LocalPlayerWeapon.CurrentAmmoTemplate != null)
                                    {
                                        float travelTime = player.Distance / Main.LocalPlayerWeapon.CurrentAmmoTemplate.InitialSpeed;
                                        destination += player.Player.Velocity * travelTime;
                                        destination -= Main.LocalPlayer.Velocity * Time.deltaTime;
                                        if (distance > 50f)
                                        {
                                            destination = destination + Vector3.up * BulletDrop(Main.LocalPlayer.Fireport.position, destination, Main.LocalPlayerWeapon.CurrentAmmoTemplate.InitialSpeed);
                                        }
                                    }
                                    target = destination;
                                }
                                target = destination;
                            }
                        }
                    }
                }

                if (target != Vector3.zero)
                {
                    AimAtPos(target);
                }
            }
        }

        public void OnGUI()
        {
            if (Settings.SilentTargetLines && (Settings.SilentAim || Settings.MagicBullet) && Main.GameWorld != null)
            {
                Weapon weapon = Main.LocalPlayerWeapon;
                if (weapon != null && Target != null)
                {
                    Vector3 aimbonePosition = GameUtils.GetBonePosByID(Target.Player, Settings.Aimbone);                  
                    Vector3 screenAimbonePosition = Camera.main.WorldToScreenPoint(aimbonePosition);
                    screenAimbonePosition.y = Screen.height - screenAimbonePosition.y;

                    // Draw the line
                    Render.DrawLine(GameUtils.ScreenCenter, screenAimbonePosition, 1f, Color.red);
                }
            }



            for (int i = 0; i < TracerList.Count; i++)
            {
                var tracer = TracerList[i];

                if (Mathf.Abs(tracer.Time - Time.time) > 2f)
                    TracerList.RemoveAt(i);
                else
                    Render.MatrixLine(tracer.StartPos, tracer.EndPos, new Color32(255, 255, 255, 30));
            }
        }

        private void NoRecoil()
        {
            if (Settings.NoRecoil)
            {
                if (Main.LocalPlayer == null)
                    return;
                Main.LocalPlayer.ProceduralWeaponAnimation.Shootingg.NewShotRecoil.RecoilEffectOn = false;
                Main.LocalPlayer.ProceduralWeaponAnimation.MotionReact.Intensity = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.Breath.Intensity = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsRotation.Current.x = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsRotation.Current.y = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsRotation.Current.z = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsPosition.Current.x = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsPosition.Current.y = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.HandsContainer.HandsPosition.Current.z = 0f;
                Main.LocalPlayer.ProceduralWeaponAnimation.WalkEffectorEnabled = false;
            }
            if (!Settings.NoRecoil)
            {
                if (Main.LocalPlayer == null)
                    return;
                Main.LocalPlayer.ProceduralWeaponAnimation.Shootingg.NewShotRecoil.RecoilEffectOn = true;
                Main.LocalPlayer.ProceduralWeaponAnimation.MotionReact.Intensity = 1f;
                Main.LocalPlayer.ProceduralWeaponAnimation.Breath.Intensity = 1f;
                Main.LocalPlayer.ProceduralWeaponAnimation.WalkEffectorEnabled = true;
            }
        }

        public static float CaulculateInFov(Vector3 position1)
        {
            Vector3 position2 = Main.MainCamera.transform.position;
            Vector3 forward = Main.MainCamera.transform.forward;
            Vector3 normalized = (position1 - position2).normalized;
            return Mathf.Acos(Mathf.Clamp(Vector3.Dot(forward, normalized), -1f, 1f)) * 57.29578f;
        }

        public static void AimAtPos(Vector3 position)
        {
            Vector3 b = Main.LocalPlayer.Fireport.position - Main.LocalPlayer.Fireport.up * 1f;
            Vector3 eulerAngles = Quaternion.LookRotation((position - b).normalized).eulerAngles;

            if (eulerAngles.x > 180f)
                eulerAngles.x -= 360f;

            Main.LocalPlayer.MovementContext.Rotation = new Vector2(eulerAngles.y, eulerAngles.x);
        }

        public static float BulletDrop(Vector3 startVector, Vector3 endVector, float BulletSpeed)
        {
            float Distance = Vector3.Distance(startVector, endVector);
            if (Distance >= 50f)
            {
                float TravelTime = Distance / BulletSpeed;
                return (float)(4.905 * TravelTime * TravelTime);
            }
            return 0f;
        }
    }
}