﻿using System;
using System.Collections.Generic;
using Comfort.Common;
using EFT;
using EFT.Interactive;
using stupid.solutions.Data;
using stupid.solutions.Features;
using stupid.solutions.Features.ESP;
using stupid.solutions.Utils;
using UnityEngine;
using System.Linq;
using EFT.Hideout;
using EFT.UI.DragAndDrop;
using System.Collections;
using System.IO;
using EFT.InventoryLogic;
using System.Reflection;
using EFT.UI.Matchmaker;
using System.Security.Policy;
using static System.Collections.Specialized.BitVector32;
using EFT.Game.Spawning;
using EFT.Counters;
using EFT.Quests;


namespace stupid.solutions
{
    public class Main : MonoBehaviour
    {
        public static List<GamePlayer> GamePlayers = new List<GamePlayer>();
        public static Player LocalPlayer;
        public static LootItem LootItem;
        public static GameWorld GameWorld;
        public static Camera MainCamera;
        public static RaidSettings raidSettings;
        public static ERaidMode raidMode;
        public static EFT.InventoryLogic.Weapon LocalPlayerWeapon;
        private float _nextPlayerCacheTime;
        private static readonly float _cachePlayersInterval = 4f;
        private static readonly Array _bodyParts = Enum.GetValues(typeof(EBodyPart));
        public static NetworkPlayer networkPlayer;
        public static List<Throwable> Grenades = new List<Throwable>();
        public static LocalRaidSettings localRaidSettings;
        private List<WishlistItem> _wishlistItems = new List<WishlistItem>();
        internal static List<GameCorpse> Corpses = new List<GameCorpse>();     
        public static ELevelType levelType;
        public static List<Location> locations = new List<Location>();
        public static ItemViewFactory itemViewFactory;
        public static List<IPlayer> Players = new List<IPlayer>();
        public static AssetBundle bundle;
        private string _logFilePath;
        private AssetBundle MenuBundle;
        private BotsController botsController;
        public string soundName = "startup";
        private Vector3 thirdPersonOffset = new Vector3(0, 2, -5); //
        private AudioSource audioSource;
        private static bool isQuestHooked = false;
        private EFT.Quests.ConditionCounterManager _conditionCounterManager;
        private bool shouldrecache = false;

        public void Awake()
        {
            GameObject hookObject = new GameObject();
            hookObject.AddComponent<Menu2>();
            hookObject.AddComponent<PlayerESP>();
            hookObject.AddComponent<ItemESP>();
            hookObject.AddComponent<LootableContainerESP>();
            hookObject.AddComponent<ExfiltrationPointsESP>();
            hookObject.AddComponent<Aimbot>();
            hookObject.AddComponent<Crosshairetc>();
            hookObject.AddComponent<CorpseEsp>();
            hookObject.AddComponent<Radar>();
            hookObject.AddComponent<locationsfixer>();
            audioSource = hookObject.AddComponent<AudioSource>();

            DontDestroyOnLoad(hookObject);

            _logFilePath = Path.Combine(Application.persistentDataPath, "log.txt");
            StartCoroutine(LoadAssetBundle());
            StartCoroutine(LoadMenuBundle());
            Settings.SaveSettings();
        }
        public void Log(string message)
        {
            using (StreamWriter writer = new StreamWriter(_logFilePath, true))
            {
                writer.WriteLine($"{DateTime.Now}: {message}");
            }
        }
        private IEnumerator LoadAssetBundle()
        {
            
            string bundlePath = Path.Combine(Application.persistentDataPath, "bundle");          
            AssetBundleCreateRequest bundleRequest = AssetBundle.LoadFromFileAsync(bundlePath);
            yield return bundleRequest;
            bundle = bundleRequest.assetBundle;
            if (bundle == null)
            {              
                yield break;
            }       
        }
        private IEnumerator LoadMenuBundle()
        {         
            string menuBundlePath = Path.Combine(Application.persistentDataPath, "menu");
            AssetBundleCreateRequest menuBundleRequest = AssetBundle.LoadFromFileAsync(menuBundlePath);
            yield return menuBundleRequest;

            MenuBundle = menuBundleRequest.assetBundle;
            if (MenuBundle == null)
            {               
                yield break;
            }
            
            AudioClip clip = MenuBundle.LoadAsset<AudioClip>(soundName);
            if (clip == null)
            {               
                yield break;
            } 
            audioSource.clip = clip;
            audioSource.volume = 0.3f;
            audioSource.Play();

            SetupUI();
        }
        private void SetupUI()
        {
            try
            {
                var prefab = MenuBundle.LoadAsset<GameObject>("Menu"); 

                if (prefab != null)
                {
                    var menuInstance = Instantiate(prefab);
                    DontDestroyOnLoad(menuInstance);

                    Menu2 menu2 = menuInstance.GetComponent<Menu2>();
                    if (menu2 != null)
                    {
                        menu2.Initialize();
                    }
                }
                else
                {
                    Log("Failed to load MenuPrefab from Menu AssetBundle!");
                }


            }
            catch (Exception e)
            {
                Log($"Exception during SetupUI: {e.Message}");
            }
        }

        public void Update()
        {       
            if (Settings.Update)
            {
                if (Time.time >= _nextPlayerCacheTime)
                {
                    GameWorld = Singleton<GameWorld>.Instance;
                    MainCamera = Camera.main;

                    if ((GameWorld != null) && (GameWorld.RegisteredPlayers != null))
                    {
                        GamePlayers.Clear();

                        foreach (Player player in GameWorld.RegisteredPlayers)
                        {
                            if (player.IsYourPlayer)
                            {
                                LocalPlayer = player;
                                continue;
                            }

                            if (!GameUtils.IsPlayerAlive(player) || (Vector3.Distance(MainCamera.transform.position, player.Transform.position) > Settings.DrawPlayersDistance))
                                continue;

                            GamePlayers.Add(new GamePlayer(player));
                        }

                        _nextPlayerCacheTime = (Time.time + _cachePlayersInterval);
                    }
                }

                if(GameWorld != null)
                {
                    foreach (GamePlayer gamePlayer in GamePlayers)
                        gamePlayer.RecalculateDynamics();
                }
            }
            
            if (Input.GetKeyDown(Settings.ClearCache))
            {
                GamePlayers.Clear();
            }
            if(GameWorld == null & !shouldrecache)
            {
                GamePlayers.Clear();
                shouldrecache = true;
            }
            if (GameWorld != null)
            {
                shouldrecache = false;
            }


            if (GameWorld != null)
            {

                if (Input.GetKeyDown(Settings.UnlockDoors))//cleared
                {
                    if (GameWorld != null)
                    {
                        foreach (var door in FindObjectsOfType<Door>())
                        {
                            if (door.DoorState == EDoorState.Open || Vector3.Distance(door.transform.position, LocalPlayer.Position) > 20f)
                                continue;

                            door.DoorState = EDoorState.Shut;
                        }
                    }

                }
                if (Input.GetKeyDown(KeyCode.KeypadPlus))//cleared
                {
                    LocalPlayer.Skills.Strength.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Endurance.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Crafting.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Strength.SetLevel(51);
                    LocalPlayer.Skills.Endurance.SetLevel(51);
                    LocalPlayer.Skills.Crafting.SetLevel(51);
                    LocalPlayer.Skills.HideoutManagement.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.HideoutManagement.SetLevel(51);
                    LocalPlayer.Skills.Attention.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Attention.SetLevel(51);
                    LocalPlayer.Skills.Vitality.SetLevel(51);
                    LocalPlayer.Skills.Vitality.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Surgery.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Surgery.SetLevel(51);
                    LocalPlayer.Skills.StressResistance.SetLevel(51);
                    LocalPlayer.Skills.StressResistance.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Health.SetLevel(51);
                    LocalPlayer.Skills.Health.SetPointsEarnedInSession(50000);
                    LocalPlayer.Skills.Search.SetLevel(51);
                    LocalPlayer.Skills.Search.SetPointsEarnedInSession(50000);

                }
                if (Input.GetKeyDown(KeyCode.F3))
                {
                    Settings.ThirdPerson = !Settings.ThirdPerson;
                }
                if (Settings.ThirdPerson)
                {
                    SwitchToThirdPerson();
                }
                else
                {
                    SwitchToFirstPerson();
                }

                if (Settings.Flyhack)
                {
                    if (Input.GetKey(KeyCode.Space))
                    {
                        LocalPlayer.MovementContext.IsGrounded = true;
                        LocalPlayer.MovementContext.FreefallTime = -0.4f;
                    }
                }
                if (Settings.Speedhack)
                {
                    if (Input.GetKey(KeyCode.W))
                        LocalPlayer.Transform.position += Main.LocalPlayer.Transform.forward / 5f * Settings.Speed;
                    if (Input.GetKey(KeyCode.S))
                        LocalPlayer.Transform.position -= Main.LocalPlayer.Transform.forward / 5f * Settings.Speed;
                    if (Input.GetKey(KeyCode.A))
                        LocalPlayer.Transform.position -= Main.LocalPlayer.Transform.right / 5f * Settings.Speed;
                    if (Input.GetKey(KeyCode.D))
                        LocalPlayer.Transform.position += Main.LocalPlayer.Transform.right / 5f * Settings.Speed;
                }

                if (Settings.Infstamina)
                {
                    LocalPlayer.Physical.StaminaParameters.AimDrainRate = 0f;
                    LocalPlayer.Physical.StaminaParameters.SprintDrainRate = 0f;
                    LocalPlayer.Physical.StaminaParameters.JumpConsumption = 0f;
                    LocalPlayer.Physical.StaminaParameters.ExhaustedMeleeSpeed = 10000f;
                }
                if (Settings.Instaheal)//innocent
                {
                    if (Input.GetKey(Settings.Instahealkey))
                    {
                        foreach (EBodyPart bodyPart in _bodyParts)
                        {
                            if (LocalPlayer.ActiveHealthController.IsBodyPartBroken(bodyPart) || LocalPlayer.ActiveHealthController.IsBodyPartDestroyed(bodyPart))
                                LocalPlayer.ActiveHealthController.RestoreBodyPart(bodyPart, 1);

                            LocalPlayer.ActiveHealthController.RemoveNegativeEffects(bodyPart);
                            LocalPlayer.ActiveHealthController.RemoveNegativeEffects(EBodyPart.Common);
                            LocalPlayer.ActiveHealthController.RestoreFullHealth();
                            LocalPlayer.ActiveHealthController.ChangeHealth(bodyPart, 1000000, default);
                            LocalPlayer.ActiveHealthController.ApplyDamage(bodyPart, 0, default);
                        }
                    }

                }
                if (Settings.Godmode)
                {
                    var lp = Main.LocalPlayer;
                    if (lp != null && lp.ActiveHealthController != null)
                    {
                        if (Settings.Godmode)
                        {
                            if (lp.ActiveHealthController.DamageCoeff != -1f)
                            {
                                lp.ActiveHealthController.SetDamageCoeff(-1f);
                                lp.ActiveHealthController.RemoveNegativeEffects(EBodyPart.Common);
                                lp.ActiveHealthController.RestoreFullHealth();
                                lp.ActiveHealthController.ChangeEnergy(999f);
                                lp.ActiveHealthController.ChangeHydration(999f);
                                lp.ActiveHealthController.DoPainKiller();
                            }

                            if (lp.ActiveHealthController.FallSafeHeight != 9999999f)
                            {
                                lp.ActiveHealthController.FallSafeHeight = 9999999f;
                            }
                        }

                    }
                } 


                if (Settings.flares)
                {
                    if (Input.GetKeyDown(KeyCode.RightBracket))
                    {
                       LocalPlayer.HandleFlareSuccessEvent(LocalPlayer.Transform.position, EFT.PrefabSettings.FlareEventType.Airdrop);
                    }
                    if (Input.GetKeyDown(KeyCode.LeftBracket))
                    {
                        LocalPlayer.HandleFlareSuccessEvent(LocalPlayer.Transform.position, EFT.PrefabSettings.FlareEventType.AIFollowEvent); ;
                    }
                }
                if (Settings.InstaSearch)
                {
                    LocalPlayer.Skills.AttentionEliteLuckySearch.Value = 100000;
                }
                Main.MainCamera.GetComponent<ThermalVision>().On = Settings.Thermal;
                if (Settings.Thermal)
                {
                    var component = MainCamera.GetComponent<ThermalVision>();
                    component.IsFpsStuck = false;
                    component.IsGlitch = false;
                    component.IsMotionBlurred = false;
                    component.IsNoisy = false;
                    component.IsPixelated = false;

                    component.TextureMask.Color = new Color(0f, 0f, 0f, 0f);
                    component.TextureMask.Stretch = false;
                    component.TextureMask.Size = 0f;
                }
                if (Input.GetKeyDown(Settings.KillAll))//notissue
                {
                    var gameWorld = Singleton<GameWorld>.Instance;
                    if (gameWorld != null)
                    {
                        IEnumerable<Player> players = gameWorld.AllAlivePlayersList.Where(x => !x.IsYourPlayer);
                        foreach (Player player in players)
                        {

                            if (!player.IsYourPlayer)
                            {

                                player.ActiveHealthController.Kill(EDamageType.Landmine);
                            }
                        }
                    }
                }
                if (Input.GetKeyDown(Settings.TPall))//notissue
                {
                    var gameWorld = Singleton<GameWorld>.Instance;
                    if (gameWorld != null)
                    {
                        IEnumerable<Player> players = gameWorld.AllAlivePlayersList.Where(x => !x.IsYourPlayer);
                        foreach (Player player in players)
                        {

                            if (!player.IsYourPlayer)
                            {
                                Vector3 playerPosition = LocalPlayer.gameObject.transform.position;
                                Vector3 forwardDirection = LocalPlayer.gameObject.transform.forward;

                                float distanceInFront = 5.0f;
                                Vector3 newPosition = playerPosition + forwardDirection * distanceInFront;

                                player.Teleport(newPosition, true);
                            }
                        }
                    }
                }
                Main.MainCamera.GetComponent<VisorEffect>().Intensity = Settings.NoVisor ? 0f : 1f;
            }           
            if (Settings.TeleportItems)
            {
                if (Input.GetKeyDown(Settings.teleportitem))
                {                    
                    Teleport(Settings.searchitem);
                    
                }
            }
            if (Settings.instahit && Main.LocalPlayer.HandsController.Item is Weapon)
            {
                Main.LocalPlayerWeapon.Template.Velocity = 1000f;
                Main.LocalPlayerWeapon.CurrentAmmoTemplate.PenetrationChanceObstacle = 100000;
                Main.LocalPlayerWeapon.CurrentAmmoTemplate.PenetrationPower = 1000000;
                Main.LocalPlayerWeapon.CurrentAmmoTemplate.RicochetChance = 0f;
                            
            }
            if (Settings.nojam && Main.LocalPlayer.HandsController.Item is Weapon)
            {
                
                //Main.LocalPlayerWeapon.Template.AllowMisfire = false;
                //Main.LocalPlayerWeapon.Template.AllowOverheat = false;
                
            }
            if (Settings.firerate && Main.LocalPlayer.HandsController.Item is Weapon)
            {               
                //LocalPlayer.GetComponent<Player.FirearmController>().Item.Template.BoltAction = false;
                //LocalPlayer.GetComponent<Player.FirearmController>().Item.Template.bFirerate = 1000;
            }
            
            if (Settings.changemaincamfov)
            {
                MainCamera.fieldOfView = Settings.maincamfov;
            }
            if (Settings.CompleteQuests)
            {
                //QuestCompleterHookd.Init();
            }
        }
        private void SwitchToThirdPerson()
        {
            if (Main.LocalPlayer != null && Main.MainCamera != null)
            {
                Main.LocalPlayer.PointOfView = EPointOfView.ThirdPerson;                
                Vector3 thirdPersonCameraPosition = Main.LocalPlayer.Position + thirdPersonOffset;

                // Update the camera position
                Main.LocalPlayer.CameraPosition.position = thirdPersonCameraPosition;
                Main.LocalPlayer.CameraPosition.LookAt(Main.LocalPlayer.LookDirection);
            }
        }

        private void SwitchToFirstPerson()
        {
            if (Main.LocalPlayer != null && Main.MainCamera != null)
            {
                Main.LocalPlayer.PointOfView = EPointOfView.FirstPerson;

                
            }
        }

        public static string TranslateBossName(string name)
        {
            if (name == "Килла")
                return "Killa";
            else if (name == "Решала")
                return "Reshala";
            else if (name == "Глухарь")
                return "Gluhar";
            else if (name == "Штурман")
                return "Shturman";
            else if (name == "Санитар")
                return "Sanitar";
            else if (name == "Тагилла")
                return "Tagilla";
            else if (name == "Зрячий")
                return "Zryachiy";
            else if (name == "Кабан")
                return "Kaban";
            else if (name == "Дед Мороз")
                return "Santa";
            else if (name == "Коллонтай")
                return "Kollontay";
            else if (name == "Big Pipe")
                return "Big Pipe";
            else if (name == "Birdeye")
                return "Birdeye";
            else if (name == "Knight")
                return "Death Knight";

            return "";
        }
        public static bool IsSuperrare(string SuperName)
        {
            if (SuperName == "TerraGroup Labs keycard (Green)" || SuperName == "TerraGroup Labs keycard (Red)"
                            || SuperName == "TerraGroup Labs keycard (Blue)" || SuperName == "TerraGroup Labs keycard (Violet)"
                            || SuperName == "TerraGroup Labs keycard (Yellow)" || SuperName == "TerraGroup Labs keycard (Black)"
                            || SuperName == "UVSR Taiga-1 survival machete" || SuperName == "Red Rebel ice pick"
                            || SuperName == "Dorm room 314 marked key" || SuperName == "Chekannaya 15 apartment key"
                            || SuperName == "Graphics card" || SuperName == "Physical Bitcoin"
                            || SuperName == "Intelligence folder" || SuperName == "LEDX Skin Transilluminator"
                            || SuperName == "TerraGroup Labs access keycard" || SuperName == "Bottle of Fierce Hatchling moonshine"
                            || SuperName == "Portable defibrillator" || SuperName == "RB-PKPM marked key"
                            || SuperName == "Tetriz portable game console" || SuperName == "Bronze lion figurine"
                            || SuperName == "Virtex programmable processor" || SuperName == "Military power filter"
                            || SuperName == "VPX Flash Storage Module" || SuperName == "Relaxation room key"
                            || SuperName == "Phased array element" || SuperName == "Military COFDM Wireless Signal Transmitter"
                            || SuperName == "Can of thermite" || SuperName == "Gold skull ring"
                            || SuperName == "Golden Star balm" || SuperName == "Chain with Prokill medallion"
                            || SuperName == "GreenBat lithium battery" || SuperName == "Roler Submariner gold wrist watch"
                            || SuperName == "Ophthalmoscope" || SuperName == "Iridium military thermal vision module"
                            || SuperName == "Car dealership closed section key" || SuperName == "RB-BK marked key"
                            || SuperName == "RB-VO marked key" || SuperName == "Keycard with a blue marking"
                            || SuperName == "Mysterious room marked key" || SuperName == "Abandoned factory marked key"
                            || SuperName == "Health Resort west wing room 216 key" || SuperName == "Cottage back door key"
                            || SuperName == "ULTRA medical storage key" || SuperName == "Kiba Arms outer door key"
                            || SuperName == "Health Resort office key with a blue tape" || SuperName == "RB-PKPM marked key"
                            || SuperName == "Health Resort west wing room 301 key" || SuperName == "Health Resort east wing room 226 key"
                            || SuperName == "Health Resort west wing room 218 key" || SuperName == "TerraGroup Labs weapon testing area key"
                            || SuperName == "Shared bedroom marked key" || SuperName == "EMERCOM medical unit key"
                            || SuperName == "Factory emergency exit key" || SuperName == "Relaxation room key" || SuperName == "Loot Lord plushie" ||
                            SuperName == "Golden egg" ||
                            SuperName == "Axel parrot figurine" || SuperName == "BEAR Buddy plush toy" || SuperName == "BEAR operative figurine"
                            || SuperName == "USEC operative figurine" || SuperName == "Cultist figurine" || SuperName == "Ded Moroz figurine"
                            || SuperName == "Den figurine" || SuperName == "Killa figurine" || SuperName == "Tagilla figurine"
                            || SuperName == "Reshala figurine" || SuperName == "Politician Multkevich figurine" || SuperName == "Scav figurine" || 
                            SuperName == "Far-forward GPS Signal Amplifier Unit" || SuperName == "Advanced current converter" || SuperName == "Microcontroller board")
                            
                return true;
            else
                return false;
        }
        public static bool IsKappa(string KappaName)
        {
            //non superrare items
            string[] kappaItems = {"Old firesteel", "Antique axe", "FireKlean gun lube", "Golden rooster figurine", "Silver Badge",
               "Deadlyslob's beard oil", "Golden 1GPhone smartphone", "Jar of DevilDog mayo", "Can of sprats",
                "Fake mustache", "Kotton beanie", "Can of Dr. Lupo's coffee beans", "Pestily plague mask",
                "Shroud half-mask", "42 Signature Blend English Tea", "Smoke balaclava", "Evasion armband",
                "Can of RatCola soda", "WZ Wallet", "LVNDMARK's rat poison", "Missam forklift key",
                "Video cassette with the Cyborg Killer movie", "BakeEzy cook book", "JohnB Liquid DNB glasses",
                "Glorious E lightweight armored mask", "Baddie's red beard", "DRD body armor", "Gingy keychain",
                "Press pass (issued for NoiceGuy)","Veritas guitar pick"};       
            //also superrare
            string[] superRareItems = {
                "Raven figurine","Axel parrot figurine", "BEAR Buddy plush toy",
                "Golden egg", "Battered antique book", "Loot Lord plushie"
        };

            
            if (kappaItems.Contains(KappaName))
            {
                return true;
            }

            
            if (!Settings.superrare && superRareItems.Contains(KappaName))
            {
                return true;
            }

            return false;
        }
        public static bool SearchedItem(string LocalizedName)
        {
            
            if (LocalizedName.Contains(Menu2.publicSearchString))
                return true;
            else return false;
        }



        public static bool IsBossByName(string name)
        {
            if (name == "Килла" || name == "Решала" || name == "Глухарь" || name == "Штурман" || name == "Санитар" || name == "Тагилла" || name == "Зрячий" || name == "Кабан" || name == "Big Pipe" || name == "Birdeye" || name == "Knight" || name == "Дед Мороз" || name == "Коллонтай")
                return true;
            else
                return false;
        }
        public static bool Isdeadbody(string LocalizedName)
        {
            if (LocalizedName == "Default Inventory")
                return true;
            else
                return false;
        }
        public static bool IsRussian(string name)
        {
            string[] russianNames = {
        "Акула", "Балу", "Барракуда", "Барс", "Беркут", "Дикобраз", "Гадыука",
        "Гепард", "Гриф", "Гриззли", "Гыурза", "Ирбис", "Ягуар", "Калан",
        "Каракурт", "Каыман", "Кобра", "Кондор", "Крачун", "Красныы", "Кречет",
        "Леопард", "Лев", "Лис", "Логгерхед", "Мангуст", "Мантис", "Манул",
        "Медвед", "Могилник", "Носорог", "Орел", "Орлан", "Падалсчик", "Пантера",
        "Пчел", "Пираныа", "Питон", "Пума", "Росомаха", "Рыс", "Сапсан",
        "Секач", "Шакал", "Скорпион", "Стервыатник", "Тарантул", "Таыпан",
        "Тигр", "Варан", "Вепр", "Волк", "Ворон", "Ыастреб", "Зубр" };


            return russianNames.Contains(name);
        }
        public static bool IsRussianC(string name)
        {
            if (name.Contains("и") || name.Contains("н")
                || name.Contains("ф") || name.Contains("Ш")
                || name.Contains("Б") || name.Contains("Д")
                || name.Contains("л") || name.Contains("ь")
                || name.Contains("т") || name.Contains("Г")
                || name.Contains("щ") || name.Contains("й")
                || name.Contains("ц") || name.Contains("ч")
                || name.Contains("я") || name.Contains("ы")
                || name.Contains("ё") || name.Contains("э")
                || name.Contains("З")
                || name.Contains("г"))
                return true;

            return false;
        }
        public static bool IsRogue(string name)
        {
            string[] rogueNames = {
        "Afraid", "Aimbotkin", "Andresto", "Applejuice", "Arizona", "Auron", "Badboy",
        "Baddie", "Beard", "Beverly", "Bison", "Blackbird", "Blade", "Blakemore",
        "Boatswain", "Boogerman", "Brockley", "Browski", "Bullet", "Bunny", "Butcher",
        "Chester", "Churchill", "Cliffhanger", "Condor", "Cook", "Corsair", "Cougar",
        "Coyote", "Crooked", "Cross", "Dakota", "Dawg", "Deceit", "Denver", "Diggi",
        "Donutop", "Duke", "Dustin", "Enzo", "Esquilo", "Father", "Firion", "Floridaman",
        "Foxy", "Frenzy", "Garandthumb", "Goat", "Golden", "Grandpa", "Greyzone", "Grim",
        "Grommet", "Gunporn", "Handsome", "Haunted", "Hellshrimp", "Honorable", "Hypno",
        "Instructor", "Iowa", "Ironfists", "James", "Jeff", "Jersey", "John", "Juggernaut",
        "Justkilo", "Kanzas", "Kentucky", "Kry", "Lancaster", "Lee", "Legia", "Litton",
        "Lost", "Lunar", "Madknight", "Mamba", "Marooner", "Meldon", "Melo", "Michigan",
        "Mike", "Momma", "Mortal", "Nevada", "Nine-hole", "Noisy", "Nukem", "Ocean",
        "Oklahoma", "OneEye", "Oskar", "Panther", "Philbo", "Quebec", "Raccoon", "Rage",
        "Rambo", "Rassler", "Rib-eye", "Riot", "Rock", "Rocket", "Ronflex", "Ronny",
        "RoughDog", "Scar", "Scottsdale", "Seafarer", "Shadow", "SharkBait", "Sharkkiller",
        "Sherifu", "Sherman", "Shifty", "Slayer", "Sly", "Snake", "Sneaky", "Sniperlife",
        "Solem", "Solidus", "Spectator-6", "Spyke", "Stamper", "Striker", "Texas", "Three-Teeth",
        "Trent", "Trickster", "Triggerhappy", "Two-Finger", "Vicious", "Victor", "Voodoo",
        "Voss", "Wadley", "Walker", "Weasel", "Whale-Eye", "Whisky", "Whitemane", "Woodrow",
        "Wrath", "Zed", "Zero-zero" };


            return rogueNames.Contains(name);
        }
        private static readonly List<string> guardNames = new List<string>
        {
           "Светлоозерскиы", "Баклажан",
        "Бановыы", "Бармалеы", "Басмач", "Базил", "Бегежан", "Белыаш", "Бибоп",
        "Борзыы", "Чебурек", "Дихлофос", "Дуныа", "Филыа", "Фламберг", "Флинт",
        "Гиыом", "Гладиус", "Громила", "Хоптод", "Хвост", "Кант", "Карабин", "Карас",
        "Карман", "Картежник", "Каторжник", "Клеымор", "Колт", "Компот", "Копченыы",
        "Кудеыар", "Кузыа", "Лупа", "Маузер", "Мазевыы", "Медоед", "Мипошка",
        "Мошенник", "Моыдодыр", "Мыакиш", "Паромчик", "Паштет", "Покер", "Пупа",
        "Резаныы", "Штемпел", "Сифон", "Сохатыы", "Супермен", "Тулуп", "Валерыанович",
        "Валтер", "Варан", "Веган", "Веник", "Арсенал", "Басыак", "Бегемотик",
        "Человек", "Дежурка", "Фуражка", "Гиббон", "Главдур", "Козырек", "Крот",
        "Куча", "Маыор", "Николаы", "Омон", "Пепс", "Сержант", "Слонолыуб",
        "Служебка", "Старлеы", "Старшиы", "Стрелок", "Татыанка", "Учик", "Васыа",
        "Висыак", "Заводскоы", "АкуШер", "Анестезиолог", "Дерматолог", "Фармацевт",
        "ФелдШер", "Физиолог", "Главврач", "Гомеопат", "Хирург", "Иммунолог",
        "Кардиолог", "Лаборант", "Лор", "Медбрат", "Медсестра", "Невролог",
        "Окулист", "Парацетамол", "Пилыулыа", "Проктолог", "Психиатр", "Ревматолог",
        "Шприц", "Стоматолог", "Терапевт", "Травматолог", "Труповоз", "Уролог",
        "Венеролог", "Заведуыусчиы", "Жгут"
        };
        public static bool IsCultist(string name)
        {
            if (name == "Жретс")
                return true;
            else
                return false;
        }
        public static bool IsCultistfollower(string name)
        {
            if (name == "ЖретсСектант")
                return true;
            else
                return false;
        }
        public static bool IsGuard(string name)
        {
            foreach (string guardName in guardNames)
            {
                if (name == guardName)
                {
                    return true;
                }
            }
            return false;
        }



        string tpname = Menu2.publicSearchString;
        public static void Teleport(string tpname)
        {

            var items = GameWorld.LootItems.Where(x => x.Item.Name.Localized().Contains(tpname)).ToList();

            if (items.Count <= 0)
            {
                return;
            }

            foreach (var item in items)
            {
                var position = LocalPlayer.gameObject.transform.position;
                position.y += 1;

                var rb = item.gameObject.GetComponent<Rigidbody>();

                if (rb != null)
                {
                    rb.isKinematic = true;
                }

                item.gameObject.transform.position = position;

                var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                sphere.GetComponent<SphereCollider>().enabled = false;
                sphere.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
                sphere.transform.position = position;
                Destroy(sphere, 5);
            }

        }

    }
    
}