﻿using UnityEngine;
using UnityEngine.UI;
using stupid.solutions.Utils;
using TMPro;
using stupid.solutions;
using System.Collections.Generic;
using System.Linq;
using EFT.InventoryLogic;
using System;
using EFT.Counters;
using EFT;
using EFT.Interactive;
public class Menu2 : MonoBehaviour
{
    //
    public LocationInGrid Location;
    //
    [SerializeField] public GameObject menu;
    [SerializeField] public GameObject menuBar;
    [SerializeField] public GameObject watermark;
    [SerializeField] public GameObject aimbotPanel;
    [SerializeField] public GameObject playerESPPanel;
    [SerializeField] public GameObject itemESPPanel;
    [SerializeField] public GameObject miscOptionsPanel;
    [SerializeField] public GameObject settingsPanel;
    [SerializeField] public GameObject itemspawnpanel;
    [SerializeField] public Button aimbotButton;
    [SerializeField] public Button playerESPButton;
    [SerializeField] public Button itemESPButton;
    [SerializeField] public Button miscOptionsButton;
    [SerializeField] public Button settingsButton;
    // Aimbot settings
    [SerializeField] public Toggle aimbotToggle;
    [SerializeField] public TMP_Text aimbotSmoothLabel;
    [SerializeField] public Slider aimbotSmoothSlider;
    [SerializeField] public Toggle aimbotDrawFOVToggle;
    [SerializeField] public TMP_Text aimbotFOVLabel;
    [SerializeField] public Slider aimbotFOVSlider;
    [SerializeField] public Toggle noRecoilToggle;
    [SerializeField] public Toggle crosshairToggle;
    [SerializeField] public Toggle silentAimToggle;
    [SerializeField] public Toggle silentTargetLinesToggle;
    [SerializeField] public Toggle instahitToggle;
    [SerializeField] public Toggle ammodrawToggle;
    [SerializeField] public TMP_Dropdown aimboneDropdown;
    [SerializeField] public Toggle CheckVisibletoggle;
    [SerializeField] public Toggle MagicBulletToggle;
    // Player ESP settings
    [SerializeField] public Toggle drawPlayersToggle;
    [SerializeField] public Toggle drawPlayerBoxToggle;
    [SerializeField] public Toggle drawPlayerNameToggle;
    [SerializeField] public Toggle drawPlayerLineToggle;
    [SerializeField] public Toggle drawPlayerHealthToggle;
    [SerializeField] public Toggle drawWeaponToggle;
    [SerializeField] public Toggle enableSkeletonToggle;
    [SerializeField] public Toggle ChamsToggle;
    [SerializeField] public Toggle drawRadarToggle;
    [SerializeField] public Slider drawPlayersDistanceSlider;
    [SerializeField] public TMP_Text drawplayerdistancelabel;//added
    [SerializeField] public Toggle drawaimpostoggle;//to assign
    // Item ESP settings
    [SerializeField] public Toggle drawLootItemsToggle;//done
    [SerializeField] public Slider drawLootItemsDistanceSlider;//done
    [SerializeField] public TMP_Text drawlootitemsdistancelabel;// to add
    [SerializeField] public Toggle superRareItemsToggle;//done
    [SerializeField] public Toggle kappaItemsToggle;//done
    [SerializeField] public Toggle questItemsToggle;//done
    [SerializeField] public Toggle commonItemsToggle;//done
    [SerializeField] public Slider commonItemsDistanceSlider;//done
    [SerializeField] public TMP_Text commonItemsdistancelabel;
    [SerializeField] public Toggle drawLootableContainersToggle;//done
    [SerializeField] public Slider drawLootableContainersDistanceSlider;//done
    [SerializeField] public TMP_Text drawcontainerdistancelabel; // done
    [SerializeField] public Toggle bodyESPToggle;//done
    [SerializeField] public Toggle playerInventoryESPToggle;//dome
    [SerializeField] public Toggle searchItemToggle;//done
    [SerializeField] public TMP_InputField searchItemInputField;// to add
    //[SerializeField] private TextMeshProUGUI outputText;//done
    [SerializeField] public Toggle teleportItemsToggle;//done
    [SerializeField] public Toggle drawExfiltrationPointsToggle;//done
    // Misc options settings
    [SerializeField] public Toggle flyhackToggle;//done
    [SerializeField] public Toggle speedhackToggle;//done
    [SerializeField] public Slider speedSlider;
    [SerializeField] public Toggle maincamfovchanger;
    [SerializeField] public Slider maincamfov;
    [SerializeField] public Toggle infStaminaToggle;//done
    [SerializeField] public Toggle instaHealToggle;//done
    [SerializeField] public Toggle thermalVisionToggle;//done
    [SerializeField] public Toggle godmodeToggle;//done
    [SerializeField] public Toggle instaSearchToggle;//done
    [SerializeField] public Toggle noVisorToggle;//done
    [SerializeField] public Toggle flaresToggle;//done
    [SerializeField] public Toggle noJamToggle;//done
    [SerializeField] public Toggle fireRateToggle;//done
    [SerializeField] public Button resetgodmodebutton;
    [SerializeField] public Toggle showitemspawnmenu;
    [SerializeField] public Toggle weaponchams;
    [SerializeField] public TMP_InputField itemid;
    [SerializeField] public Button spawnitembutton;
    [SerializeField] public Button addxpsessionbutton;
    [SerializeField] public Slider xpslider;
    [SerializeField] public TMP_Text xpsliderlabel;

    public static string publicSearchString = "Grenade";
    public static string itemidstring = "";
    //Assign Keybinds    
    [SerializeField] public Button clearCacheKeyButton;
    [SerializeField] public TMP_Text clearCacheKeyLabel;
    [SerializeField] public Button skillKeyButton;
    [SerializeField] public TMP_Text skillKeyLabel;
    [SerializeField] public Button aimbotKeyButton;
    [SerializeField] public TMP_Text aimbotKeyLabel;
    [SerializeField] public Button unlockDoorsKeyButton;
    [SerializeField] public TMP_Text unlockDoorsKeyLabel;
    [SerializeField] public Button teleportitembutton;//done
    [SerializeField] public TMP_Text teleportitemlabel;//done
    [SerializeField] public Button killallbutton;
    [SerializeField] public TMP_Text killalllabel;
    [SerializeField] public Button tpallkeybutton;
    [SerializeField] public TMP_Text tpallkeylabel;
    [SerializeField] public Button instahealkeybutton;
    [SerializeField] public TMP_Text instahealkeylabel;




    private bool waitingForKey = false;
    private KeybindType currentKeybindType;

    public static Main main = new Main();

    private void Start()
    {
        LoadSettings(); // Load settings at the start

        Initialize();
        
        if (menu != null)
        {
            Canvas canvas = menu.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.sortingOrder = short.MaxValue;
            }

            CanvasGroup canvasGroup = menu.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
            {
                canvasGroup.blocksRaycasts = canvasGroup.alpha > 0;
                canvasGroup.ignoreParentGroups = false;
                canvasGroup.interactable = true;
            }

            menu.SetActive(true);
            aimbotPanel.SetActive(true);           
            watermark.SetActive(true);
            
        }
        if (searchItemInputField != null)
        {
            searchItemInputField.onEndEdit.AddListener(OnSearchItemInputEndEdit);
            searchItemInputField.onValueChanged.AddListener(OnSearchItemValueChanged);
        }
        if (itemid != null)
        {
            itemid.onEndEdit.AddListener(SpawnItemInputEndEdit);
            itemid.onValueChanged.AddListener(SpawnItemValueChanged);
        }
        clearCacheKeyButton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.ClearCache));
        skillKeyButton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.Skill));
        aimbotKeyButton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.Aimbot));
        unlockDoorsKeyButton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.UnlockDoors));
        teleportitembutton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.teleportitem));
        killallbutton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.killall));
        tpallkeybutton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.tpall));
        instahealkeybutton.onClick.AddListener(() => OnKeybindButtonClick(KeybindType.instaheal));
        //rest
        InitializeAimboneDropdown();
        UpdateAimbotSettingsUI();
        UpdatePlayerESPSettingsUI();
        UpdateItemESPSettingsUI();
        UpdateMiscOptionsSettingsUI();
        AddToggleListeners();
        AddSliderListeners();
        
    }

    private void LoadSettings()
    {
        Settings.LoadSettings();
        UpdateAimbotSettingsUI(); // Reflect the loaded settings in the UI
        UpdatePlayerESPSettingsUI();
        UpdateItemESPSettingsUI();
        UpdateMiscOptionsSettingsUI();
    }

    private void SaveSettings()
    {
        Settings.SaveSettings(); // Save current settings to file
    }

    public void Initialize()
    {
       
        if (aimbotButton != null) aimbotButton.onClick.AddListener(ShowAimbotPanel);
        if (playerESPButton != null) playerESPButton.onClick.AddListener(ShowPlayerESPPanel);
        if (itemESPButton != null) itemESPButton.onClick.AddListener(ShowItemESPPanel);
        if (miscOptionsButton != null) miscOptionsButton.onClick.AddListener(ShowMiscOptionsPanel);
        if (settingsButton != null) settingsButton.onClick.AddListener(ShowSettingsPanel);
        if (resetgodmodebutton != null) resetgodmodebutton.onClick.AddListener(ResetGodmode);
        if (spawnitembutton != null) spawnitembutton.onClick.AddListener(Spawnitem);
        if (addxpsessionbutton != null) addxpsessionbutton.onClick.AddListener(XPAdder);
        HideAllPanels();

        UpdateKeybindLabels();
    }
    

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Insert))
        {
            if (menuBar != null)
            {
                menuBar.SetActive(!menuBar.activeSelf);
            }
            
        }
        

        UpdateAimbotSettingsUI(); // Ensure UI reflects the current settings
        UpdatePlayerESPSettingsUI();
        UpdateItemESPSettingsUI();
        UpdateMiscOptionsSettingsUI();        
        if (itemspawnpanel != null && Settings.Itemspawnmenu)
        {
            itemspawnpanel.SetActive(true);
        }
        else if(itemspawnpanel != null)
        {
            itemspawnpanel.SetActive(false);
        } 
            
        if (waitingForKey && Input.anyKeyDown)
        {
            foreach (KeyCode keyCode in Enum.GetValues(typeof(KeyCode)))
            {
                if (Input.GetKeyDown(keyCode))
                {
                    AssignKeybind(keyCode);
                    break;
                }
            }
            waitingForKey = false;
        }
    }
    public void XPAdder()
    {
        if (Main.GameWorld = null)
            return;
        int exp = (int)Settings.expamount;
        Main.LocalPlayer.Profile.EftStats.SessionCounters.SetLong(exp, CounterTag.Exp);
    }
    public void ShowAimbotPanel()
    {
        HideAllPanels();
        aimbotPanel.SetActive(true);
    }
    
    public void ShowPlayerESPPanel()
    {
        HideAllPanels();
        playerESPPanel.SetActive(true);
    }

    public void ShowItemESPPanel()
    {
        HideAllPanels();
        itemESPPanel.SetActive(true);
    }

    public void ShowMiscOptionsPanel()
    {
        HideAllPanels();
        miscOptionsPanel.SetActive(true);
    }

    public void ShowSettingsPanel()
    {
        HideAllPanels();
        settingsPanel.SetActive(true);
    }
    public void ResetGodmode()
    {
        var lp = Main.LocalPlayer;
        if (lp != null && lp.ActiveHealthController != null)
        {
            lp.ActiveHealthController.SetDamageCoeff(1f);
            lp.ActiveHealthController.FallSafeHeight = 1f;
            lp.ActiveHealthController.ChangeEnergy(100f);
            lp.ActiveHealthController.ChangeHydration(100f);
            lp.ActiveHealthController.RemoveMedEffect();
            lp.ActiveHealthController.DoPermanentHealthBoost(0f);
            Settings.Godmode = false;

        }


    }
    public void Spawnitem()
    {
        try
        {
            ItemFactory itemFactory = new ItemFactory();
            string itemId = Menu2.itemidstring;
            Item item = itemFactory.CreateItem(itemId);
            EFT.UI.ConsoleScreen.Log($"Log: Attempting to create item with ID: {itemId}");
            if (item == null)
            {
                EFT.UI.ConsoleScreen.Log("Log: Failed to create item.");
                return;
            }

            EFT.UI.ConsoleScreen.Log("Log: Item created successfully.");

            item.SpawnedInSession = true;

            dynamic backpack = Main.LocalPlayer.Profile.Inventory.Equipment.GetSlot(EquipmentSlot.Backpack).ContainedItem;
            if (!item.QuestItem)
            {
                if (GameUtils.IsInventoryItemValid(backpack))
                {
                    //backpack.Grids[0].Add(item);
					backpack.Grids[0].AddAnywhere(item, EErrorHandlingType.Ignore); // op said this fixed item spawner
                    EFT.UI.ConsoleScreen.Log("Log: Item created successfully.");
                }
                else
                {
                    // If there is no backpack, create an Attack bag and add it to the backpack slot
                    Item newBackPackItem = itemFactory.CreateItem("5ab8ebf186f7742d8b372e80"); // Attack bag
                    if (newBackPackItem != null)
                    {
                        Main.LocalPlayer.Profile.Inventory.Equipment.GetSlot(EquipmentSlot.Backpack).Add(newBackPackItem, false, false);

                        // Add item to the newly created backpack
                        dynamic newBackpack = Main.LocalPlayer.Profile.Inventory.Equipment.GetSlot(EquipmentSlot.Backpack).ContainedItem;
						//newBackpack.Grids[0].Add(item);
						newBackpack.Grids[0].AddAnywhere(item, EErrorHandlingType.Ignore); // op said this fixed item spawner
                    }
                }
            }
            else
            {
                EFT.UI.ConsoleScreen.Log("Item Is a Quest Item, doing Nothing !");
            }
        }
        catch (Exception ex)
        {
            EFT.UI.ConsoleScreen.Log($"Log: Exception occurred: {ex.Message}");
        }
    }

    private void OnKeybindButtonClick(KeybindType keybindType)
    {
        currentKeybindType = keybindType;
        waitingForKey = true;

        switch (keybindType)
        {
            case KeybindType.ClearCache:
                clearCacheKeyLabel.text = "Press a key...";
                break;
            case KeybindType.Skill:
                skillKeyLabel.text = "Press a key...";
                break;
            case KeybindType.Aimbot:
                aimbotKeyLabel.text = "Press a key...";
                break;
            case KeybindType.UnlockDoors:
                unlockDoorsKeyLabel.text = "Press a key...";
                break;
            case KeybindType.teleportitem:
                teleportitemlabel.text = "Press a key...";
                break;
            case KeybindType.killall:
                killalllabel.text = "Press a key...";
                break;
            case KeybindType.tpall:
                tpallkeylabel.text = "Press a key...";
                break;
            case KeybindType.instaheal:
                instahealkeylabel.text = "Press a key...";
                break;
        }
    }
    private void AssignKeybind(KeyCode newKey)
    {
        switch (currentKeybindType)
        {
            case KeybindType.ClearCache:
                Settings.ClearCache = newKey;
                clearCacheKeyLabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.Skill:
                Settings.Skill = newKey;
                skillKeyLabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.Aimbot:
                Settings.AimbotKey = newKey;
                aimbotKeyLabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.UnlockDoors:
                Settings.UnlockDoors = newKey;
                unlockDoorsKeyLabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.teleportitem:
                Settings.teleportitem = newKey;
                teleportitemlabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.killall:
                Settings.KillAll = newKey;
                killalllabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.tpall:
                Settings.TPall = newKey;
                tpallkeylabel.text = "Key: " + newKey.ToString();
                break;
            case KeybindType.instaheal:
                Settings.Instahealkey = newKey;
                instahealkeylabel.text = "Key: " + newKey.ToString();
                break;
        }
    }
    void UpdateKeybindLabels()
    {
        clearCacheKeyLabel.text = "Key: " + Settings.ClearCache.ToString();
        skillKeyLabel.text = "Key: " + Settings.Skill.ToString();
        aimbotKeyLabel.text = "Key: " + Settings.AimbotKey.ToString();
        unlockDoorsKeyLabel.text = "Key: " + Settings.UnlockDoors.ToString();
        teleportitemlabel.text = "Key: " + Settings.teleportitem.ToString();
        killalllabel.text = "Key: " + Settings.KillAll.ToString();
        tpallkeylabel.text = "Key: " + Settings.TPall.ToString();
        instahealkeylabel.text = "Key: " + Settings.Instahealkey.ToString();
    }
    public void HideAllPanels()
    {
        aimbotPanel.SetActive(false);
        playerESPPanel.SetActive(false);
        itemESPPanel.SetActive(false);
        miscOptionsPanel.SetActive(false);
        settingsPanel.SetActive(false);
        
    }

    public void UpdateAimbotSettingsUI()
    {
        if (aimbotToggle != null) aimbotToggle.isOn = Settings.Aimbot;
        if (aimbotSmoothLabel != null) aimbotSmoothLabel.text = $"Aimbot Smooth {(int)Settings.AimbotSmooth}";
        if (aimbotSmoothSlider != null) aimbotSmoothSlider.value = Settings.AimbotSmooth;
        if (aimbotDrawFOVToggle != null) aimbotDrawFOVToggle.isOn = Settings.AimbotDrawFOV;
        if (aimbotFOVLabel != null) aimbotFOVLabel.text = $"Aimbot FOV {(int)Settings.AimbotFOV}";
        if (aimbotFOVSlider != null) aimbotFOVSlider.value = Settings.AimbotFOV;
        if (noRecoilToggle != null) noRecoilToggle.isOn = Settings.NoRecoil;
        if (crosshairToggle != null) crosshairToggle.isOn = Settings.Crosshair;
        if (silentAimToggle != null) silentAimToggle.isOn = Settings.SilentAim;
        if (silentTargetLinesToggle != null) silentTargetLinesToggle.isOn = Settings.SilentTargetLines;
        if (instahitToggle != null) instahitToggle.isOn = Settings.instahit;
        if (ammodrawToggle != null) ammodrawToggle.isOn = Settings.DrawInfo;
        if (CheckVisibletoggle != null) CheckVisibletoggle.isOn = Settings.CheckVisible;
        if (MagicBulletToggle != null) MagicBulletToggle.isOn = Settings.MagicBullet;
    }

    public void ApplyAimbotSettingsUI()
    {
        Settings.Aimbot = aimbotToggle.isOn;
        Settings.AimbotSmooth = aimbotSmoothSlider.value;
        Settings.AimbotDrawFOV = aimbotDrawFOVToggle.isOn;
        Settings.AimbotFOV = aimbotFOVSlider.value;
        Settings.NoRecoil = noRecoilToggle.isOn;
        Settings.Crosshair = crosshairToggle.isOn;
        Settings.SilentAim = silentAimToggle.isOn;
        Settings.SilentTargetLines = silentTargetLinesToggle.isOn;
        Settings.instahit = instahitToggle.isOn;
        Settings.DrawInfo = ammodrawToggle.isOn;
        Settings.CheckVisible = CheckVisibletoggle.isOn;
        Settings.MagicBullet = MagicBulletToggle.isOn;
        SaveSettings(); // Save settings after applying
    }
    private void InitializeAimboneDropdown()
    {
        if (aimboneDropdown != null)
        {

            aimboneDropdown.options.Clear();
            aimboneDropdown.options.Add(new TMP_Dropdown.OptionData("Head"));
            aimboneDropdown.options.Add(new TMP_Dropdown.OptionData("Neck"));
            aimboneDropdown.options.Add(new TMP_Dropdown.OptionData("Body 1"));
            aimboneDropdown.options.Add(new TMP_Dropdown.OptionData("Body 2"));


            SetDropdownValueFromSettings();

            // Add a listener for the dropdown value change
            aimboneDropdown.onValueChanged.AddListener(delegate { AimboneDropdownValueChanged(aimboneDropdown); });
        }
    }

    private void SetDropdownValueFromSettings()
    {
        var aimboneMapping = new Dictionary<int, int>
    {
        { 0, 133 }, // "Head"
        { 1, 132 }, // "Neck"
        { 2, 36  }, // "Body 1"
        { 3, 35  }  // "Body 2"
    };

        // Find the index that corresponds to the current Aimbone setting
        int dropdownIndex = aimboneMapping.FirstOrDefault(x => x.Value == Settings.Aimbone).Key;

        // Set the dropdown value if the setting matches a valid index
        if (dropdownIndex >= 0 && dropdownIndex < aimboneDropdown.options.Count)
        {
            aimboneDropdown.value = dropdownIndex;
        }
        
    }

    private void AimboneDropdownValueChanged(TMP_Dropdown dropdown)
    {
        var aimboneMapping = new Dictionary<int, int>
    {
        { 0, 133 }, // "Head"
        { 1, 132 }, // "Neck"
        { 2, 36  }, // "Body 1"
        { 3, 35  }  // "Body 2"
    };

        int selectedIndex = dropdown.value;

        // Use the index to get the corresponding Aimbone value
        if (aimboneMapping.TryGetValue(selectedIndex, out int aimboneValue))
        {
            Settings.Aimbone = aimboneValue;
            SaveSettings(); // Save settings when changed
        }
    }

    public void UpdatePlayerESPSettingsUI()
    {
        if (drawPlayersToggle != null) drawPlayersToggle.isOn = Settings.DrawPlayers;
        if (drawPlayerBoxToggle != null) drawPlayerBoxToggle.isOn = Settings.DrawPlayerBox;
        if (drawPlayerNameToggle != null) drawPlayerNameToggle.isOn = Settings.DrawPlayerName;
        if (drawPlayerLineToggle != null) drawPlayerLineToggle.isOn = Settings.DrawPlayerLine;
        if (drawPlayerHealthToggle != null) drawPlayerHealthToggle.isOn = Settings.DrawPlayerHealth;
        if (drawWeaponToggle != null) drawWeaponToggle.isOn = Settings.playerWeapon;
        if (enableSkeletonToggle != null) enableSkeletonToggle.isOn = Settings.EnableSkeleton;
        if (ChamsToggle != null) ChamsToggle.isOn = Settings.Chams;
        if (drawRadarToggle != null) drawRadarToggle.isOn = Settings.DrawRadar;
        if (drawaimpostoggle != null) drawaimpostoggle.isOn = Settings.drawaimpos;
        if (drawPlayersDistanceSlider != null) drawPlayersDistanceSlider.value = Settings.DrawPlayersDistance;
        if (drawplayerdistancelabel != null) drawplayerdistancelabel.text = $"Player Distance {(int)Settings.DrawPlayersDistance} m";
        
    }

    public void ApplyPlayerESPSettingsUI()
    {
        Settings.DrawPlayers = drawPlayersToggle.isOn;
        Settings.DrawPlayerBox = drawPlayerBoxToggle.isOn;
        Settings.DrawPlayerName = drawPlayerNameToggle.isOn;
        Settings.DrawPlayerLine = drawPlayerLineToggle.isOn;
        Settings.DrawPlayerHealth = drawPlayerHealthToggle.isOn;
        Settings.playerWeapon = drawWeaponToggle.isOn;
        Settings.EnableSkeleton = enableSkeletonToggle.isOn;       
        Settings.DrawRadar = drawRadarToggle.isOn;
        Settings.DrawPlayersDistance = drawPlayersDistanceSlider.value;
        Settings.Chams = ChamsToggle.isOn;
        Settings.drawaimpos = drawaimpostoggle.isOn;
        SaveSettings(); // Save settings after applying
    }

    public void UpdateItemESPSettingsUI()
    {
        if (drawLootItemsToggle != null) drawLootItemsToggle.isOn = Settings.DrawLootItems;
        if (drawLootItemsDistanceSlider != null) drawLootItemsDistanceSlider.value = Settings.DrawLootItemsDistance;
        if (drawlootitemsdistancelabel != null) drawlootitemsdistancelabel.text = $"Item Distance {(int)Settings.DrawLootItemsDistance} m";
        if (superRareItemsToggle != null) superRareItemsToggle.isOn = Settings.superrare;
        if (kappaItemsToggle != null) kappaItemsToggle.isOn = Settings.kappa;
        if (questItemsToggle != null) questItemsToggle.isOn = Settings.quest;
        if (commonItemsToggle != null) commonItemsToggle.isOn = Settings.common;
        if (commonItemsDistanceSlider != null) commonItemsDistanceSlider.value = Settings.commonitemdistance;
        if (commonItemsdistancelabel != null) commonItemsdistancelabel.text = $"Common Item Distance {(int)Settings.commonitemdistance} m";
        if (drawLootableContainersToggle != null) drawLootableContainersToggle.isOn = Settings.DrawLootableContainers;
        if (drawLootableContainersDistanceSlider != null) drawLootableContainersDistanceSlider.value = Settings.DrawLootableContainersDistance;
        if (drawcontainerdistancelabel != null) drawcontainerdistancelabel.text = $"Container Distance {(int)Settings.DrawLootableContainersDistance} m";
        if (bodyESPToggle != null) bodyESPToggle.isOn = Settings.BodyESP;
        if (playerInventoryESPToggle != null) playerInventoryESPToggle.isOn = Settings.PlayerInventoryESP;
        if (searchItemToggle != null) searchItemToggle.isOn = Settings.searchItem;
        if (teleportItemsToggle != null) teleportItemsToggle.isOn = Settings.TeleportItems;
        if (drawExfiltrationPointsToggle != null) drawExfiltrationPointsToggle.isOn = Settings.DrawExfiltrationPoints;
        if (ChamsToggle != null) ChamsToggle.isOn = Settings.Chams;
        if (drawaimpostoggle != null) drawaimpostoggle.isOn = Settings.drawaimpos;
    }

    public void ApplyItemESPSettingsUI()
    {
        Settings.DrawLootItems = drawLootItemsToggle.isOn;
        Settings.DrawLootItemsDistance = drawLootItemsDistanceSlider.value;
        Settings.superrare = superRareItemsToggle.isOn;
        Settings.kappa = kappaItemsToggle.isOn;
        Settings.quest = questItemsToggle.isOn;
        Settings.common = commonItemsToggle.isOn;
        Settings.commonitemdistance = commonItemsDistanceSlider.value;
        Settings.DrawLootableContainers = drawLootableContainersToggle.isOn;
        Settings.DrawLootableContainersDistance = drawLootableContainersDistanceSlider.value;
        Settings.BodyESP = bodyESPToggle.isOn;
        Settings.PlayerInventoryESP = playerInventoryESPToggle.isOn;
        Settings.searchItem = searchItemToggle.isOn;
        Settings.TeleportItems = teleportItemsToggle.isOn;
        Settings.DrawExfiltrationPoints = drawExfiltrationPointsToggle.isOn;

        SaveSettings(); // Save settings after applying
    }

    public void UpdateMiscOptionsSettingsUI()
    {
        if (flyhackToggle != null) flyhackToggle.isOn = Settings.Flyhack;
        if (speedhackToggle != null) speedhackToggle.isOn = Settings.Speedhack;
        if (speedSlider != null) speedSlider.value = Settings.Speed;
        if (maincamfovchanger != null) maincamfovchanger.isOn = Settings.changemaincamfov;
        if (maincamfov != null) maincamfov.value = Settings.maincamfov;
        if (xpslider != null) xpslider.value = Settings.expamount;
        if (xpsliderlabel != null) xpsliderlabel.text = $"XP {(int)Settings.expamount}";
        if (infStaminaToggle != null) infStaminaToggle.isOn = Settings.Infstamina;
        if (instaHealToggle != null) instaHealToggle.isOn = Settings.Instaheal;
        if (thermalVisionToggle != null) thermalVisionToggle.isOn = Settings.Thermal;
        if (godmodeToggle != null) godmodeToggle.isOn = Settings.Godmode;
        if (instaSearchToggle != null) instaSearchToggle.isOn = Settings.InstaSearch;
        if (noVisorToggle != null) noVisorToggle.isOn = Settings.NoVisor;
        if (flaresToggle != null) flaresToggle.isOn = Settings.flares;
        if (noJamToggle != null) noJamToggle.isOn = Settings.nojam;
        if (fireRateToggle != null) fireRateToggle.isOn = Settings.firerate;
        if (showitemspawnmenu != null) showitemspawnmenu.isOn = Settings.Itemspawnmenu;
        if (weaponchams != null) weaponchams.isOn = Settings.weaponchams;
    }

    public void ApplyMiscOptionsSettingsUI()
    {
        Settings.Flyhack = flyhackToggle.isOn;
        Settings.Speedhack = speedhackToggle.isOn;
        Settings.Speed = speedSlider.value;
        Settings.changemaincamfov = maincamfovchanger.isOn;
        Settings.maincamfov = maincamfov.value;
        Settings.expamount = xpslider.value;
        Settings.Infstamina = infStaminaToggle.isOn;
        Settings.Instaheal = instaHealToggle.isOn;
        Settings.Thermal = thermalVisionToggle.isOn;
        Settings.Godmode = godmodeToggle.isOn;
        Settings.InstaSearch = instaSearchToggle.isOn;
        Settings.NoVisor = noVisorToggle.isOn;
        Settings.flares = flaresToggle.isOn;
        Settings.nojam = noJamToggle.isOn;
        Settings.firerate = fireRateToggle.isOn;
        Settings.Itemspawnmenu = showitemspawnmenu.isOn;
        Settings.weaponchams = weaponchams.isOn;

        SaveSettings(); // Save settings after applying
    }
    private void OnSearchItemValueChanged(string input)
    {
        Debug.Log($"Search Item Value Changed: {input}");

        if (!string.IsNullOrWhiteSpace(input))
        {
            publicSearchString = input;
            Debug.Log($"Public search string updated: {publicSearchString}");

            //if (outputText != null)
            //{
                //outputText.text = input; // Update the display text
            //}
        }
        else
        {
            Debug.LogWarning("Input is either null or whitespace.");
        }
    }

    private void OnSearchItemInputEndEdit(string input)
    {
        Debug.Log($"Search Item Input End Edit Triggered: {input}");

        if (!string.IsNullOrWhiteSpace(input))
        {
            publicSearchString = input;
            Debug.Log($"Public search string updated: {publicSearchString}");
        }
        else
        {
            Debug.LogWarning("Input is either null or whitespace.");
        }
    }
    private void SpawnItemValueChanged(string input)
    {
        Debug.Log($"Search Item Value Changed: {input}");

        if (!string.IsNullOrWhiteSpace(input))
        {
            itemidstring = input;
            Debug.Log($"Public search string updated: {itemidstring}");

            //if (outputText != null)
            //{
                //outputText.text = input; // Update the display text
            //}
        }
        else
        {
            Debug.LogWarning("Input is either null or whitespace.");
        }
    }

    private void SpawnItemInputEndEdit(string input)
    {
        Debug.Log($"Search Item Input End Edit Triggered: {input}");

        if (!string.IsNullOrWhiteSpace(input))
        {
            itemidstring = input;
            Debug.Log($"Public search string updated: {itemidstring}");
        }
        else
        {
            Debug.LogWarning("Input is either null or whitespace.");
        }
    }


    public void AddToggleListeners()
    {
        if (aimbotToggle != null) aimbotToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (noRecoilToggle != null) noRecoilToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (crosshairToggle != null) crosshairToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (silentAimToggle != null) silentAimToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (silentTargetLinesToggle != null) silentTargetLinesToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (instahitToggle != null) instahitToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (ammodrawToggle != null) ammodrawToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (CheckVisibletoggle != null) CheckVisibletoggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (MagicBulletToggle != null) MagicBulletToggle.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        //searchbar

        //searchbar
        if (drawPlayersToggle != null) drawPlayersToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawPlayerBoxToggle != null) drawPlayerBoxToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawPlayerNameToggle != null) drawPlayerNameToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawPlayerLineToggle != null) drawPlayerLineToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawPlayerHealthToggle != null) drawPlayerHealthToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawWeaponToggle != null) drawWeaponToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (enableSkeletonToggle != null) enableSkeletonToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawRadarToggle != null) drawRadarToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (ChamsToggle != null) ChamsToggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawaimpostoggle != null) drawaimpostoggle.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });

        if (drawLootItemsToggle != null) drawLootItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (superRareItemsToggle != null) superRareItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (kappaItemsToggle != null) kappaItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (questItemsToggle != null) questItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (commonItemsToggle != null) commonItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (drawLootableContainersToggle != null) drawLootableContainersToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (bodyESPToggle != null) bodyESPToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (playerInventoryESPToggle != null) playerInventoryESPToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (searchItemToggle != null) searchItemToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (teleportItemsToggle != null) teleportItemsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (drawExfiltrationPointsToggle != null) drawExfiltrationPointsToggle.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });

        if (flyhackToggle != null) flyhackToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (speedhackToggle != null) speedhackToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (maincamfovchanger != null) maincamfovchanger.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (infStaminaToggle != null) infStaminaToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (instaHealToggle != null) instaHealToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (thermalVisionToggle != null) thermalVisionToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (godmodeToggle != null) godmodeToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (instaSearchToggle != null) instaSearchToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (noVisorToggle != null) noVisorToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (flaresToggle != null) flaresToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (noJamToggle != null) noJamToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (fireRateToggle != null) fireRateToggle.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (showitemspawnmenu != null) showitemspawnmenu.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (weaponchams != null) weaponchams.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        SaveSettings();
    }
    private void AddSliderListeners()
    {
        if (aimbotSmoothSlider != null) aimbotSmoothSlider.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });
        if (aimbotFOVSlider != null) aimbotFOVSlider.onValueChanged.AddListener(delegate { ApplyAimbotSettingsUI(); });

        if (drawPlayersDistanceSlider != null) drawPlayersDistanceSlider.onValueChanged.AddListener(delegate { ApplyPlayerESPSettingsUI(); });
        if (drawLootItemsDistanceSlider != null) drawLootItemsDistanceSlider.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (commonItemsDistanceSlider != null) commonItemsDistanceSlider.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (drawLootableContainersDistanceSlider != null) drawLootableContainersDistanceSlider.onValueChanged.AddListener(delegate { ApplyItemESPSettingsUI(); });
        if (speedSlider != null) speedSlider.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (maincamfov != null) maincamfov.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
        if (xpslider != null) xpslider.onValueChanged.AddListener(delegate { ApplyMiscOptionsSettingsUI(); });
    }
    private enum KeybindType
    {
        ClearCache,
        Skill,
        Aimbot,
        UnlockDoors,
        teleportitem,
        killall,
        tpall,
        instaheal
    }
}