﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using Comfort.Common;
using EFT;
using EFT.HealthSystem;
using EFT.Interactive;
using stupid.solutions.Data;
using stupid.solutions.Utils;
using UnityEngine;
using static stupid.solutions.Main;
using EFT.UI;
using EFT.NextObservedPlayer;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using EFT.InventoryLogic;
using EFT.UI.Ragfair;
using EFT.Ballistics;
using System.Collections;
using BSG.CameraEffects;
using JetBrains.Annotations;
using UnityEngine.Rendering;
using System.Runtime.InteropServices;
using System.Reflection;
using EFT.CameraControl;
using System.ComponentModel;
using Diz.LanguageExtensions;
using Diz.Skinning;
using static CC_Vintage;
using static System.Collections.Specialized.BitVector32;
using System.Runtime.ConstrainedExecution;
using System.Runtime.InteropServices.ComTypes;
using GPUInstancer;
using EFT.Visual;
using static VisorEffect;
using System.Runtime.CompilerServices;
using System.IO;
using stupid.solutions;
using static stupid.solutions.Features.ESP.PlayerESP;
using System.Diagnostics.Eventing.Reader;

namespace stupid.solutions.Features.ESP
{


    public class PlayerESP : MonoBehaviour
    {
        private static readonly Color _playerColor = Color.green;
        private static readonly Color _botColor = Color.yellow;
        private static readonly Color _healthColor = Color.green;
        private static readonly Color _bossColor = Color.red;
        private static readonly Color _raiderColor = Color.magenta;
        public float lineLength = 20f;
        public float lineThickness = 2f;
        private static IEnumerator<Item> _equipItemList;
        Color ItemColor = Color.white;
        private string _logFilePath;
        private static HashSet<int> processedEntities = new HashSet<int>();
        private static WildSpawnType WildSpawnType;
        private void Awake()
        {
            _logFilePath = Path.Combine(Application.persistentDataPath, "log.txt");
        }
        private void Log(string message)
        {
            using (StreamWriter writer = new StreamWriter(_logFilePath, true))
            {
                writer.WriteLine($"{DateTime.Now}: {message}");
            }
        }
        public void OnGUI()
        {
            if (!Settings.DrawPlayers)
                return;
            if (Settings.Chams)
            {
                Chams();
            }


            foreach (GamePlayer gamePlayer in Main.GamePlayers)
            {
                if (!gamePlayer.IsOnScreen || gamePlayer.Distance > Settings.DrawPlayersDistance || gamePlayer.Player == Main.LocalPlayer)
                    continue;

                Color playerColor = ((gamePlayer.IsAI) ? _botColor : _playerColor);

                float boxPositionY = (gamePlayer.HeadScreenPosition.y - 10f);
                float boxHeight = (Math.Abs(gamePlayer.HeadScreenPosition.y - gamePlayer.ScreenPosition.y) + 10f);
                float boxWidth = (boxHeight * 0.65f);

                if (Settings.DrawPlayerBox)
                {
                    Vector2 boxPosition = new Vector2(gamePlayer.ScreenPosition.x - (boxWidth / 2f), boxPositionY);
                    Vector2 boxSize = new Vector2(boxWidth, boxHeight);
                    Render.DrawCornerBox(boxPosition, boxSize, 2f, playerColor, 5f, centered: false);
                }

                if (Settings.DrawPlayerHealth)
                {
                    if (gamePlayer.Player.HealthController.IsAlive)
                    {
                        float currentPlayerHealth = gamePlayer.Player.HealthController.GetBodyPartHealth(EBodyPart.Common).Current;
                        float maximumPlayerHealth = gamePlayer.Player.HealthController.GetBodyPartHealth(EBodyPart.Common).Maximum;


                        int currentPlayerHealthInt = (int)Math.Floor(currentPlayerHealth);
                        int maximumPlayerHealthInt = (int)Math.Floor(maximumPlayerHealth);

                        float healthBarHeight = GameUtils.Map(currentPlayerHealth, 0f, maximumPlayerHealth, 0f, boxHeight);

                        Color healthBarColor = currentPlayerHealth / maximumPlayerHealth < 0.5f ? Color.red : _healthColor;
                        float healthBarX = gamePlayer.ScreenPosition.x - (boxWidth / 2f) - 3f;
                        float healthBarBottomY = boxPositionY + boxHeight;

                        Render.DrawLine(new Vector2(healthBarX, healthBarBottomY - healthBarHeight), new Vector2(healthBarX, healthBarBottomY), 3f, healthBarColor);
                        //Vector2 stringPosition = new Vector2(healthBarX - 40f, healthBarBottomY - 10f);
                        //string healthText = $" {currentPlayerHealthInt}/{maximumPlayerHealthInt} ";
                        //Render.DrawString(stringPosition, healthText, Color.green);
                    }
                }

                if (Settings.DrawPlayerName)
                {
                    string playerText = "  ";

                    if (gamePlayer.Player.Profile.Info.Settings.IsBoss())
                    {
                        bool IsBoss = Main.IsBossByName(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsRaider = Main.IsRussian(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsGuard = Main.IsGuard(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsRogue = Main.IsRogue(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsCultist = Main.IsCultist(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsCultistFollower = Main.IsCultistfollower(gamePlayer.Player.Profile.Info.Nickname.Localized());

                        if (IsBoss)
                        {
                            playerText = Main.TranslateBossName(gamePlayer.Player.Profile.Info.Nickname.Localized()) + $"  [{gamePlayer.FormattedDistance}] ";
                            playerColor = _bossColor;
                        }
                        else if (IsGuard)
                        {
                            playerText = $"Guard [{gamePlayer.FormattedDistance}] ";
                            playerColor = Color.red;
                        }

                        else if (IsRaider)
                        {
                            playerText = $" Raider[{gamePlayer.FormattedDistance}] ";
                            playerColor = _raiderColor;
                        }
                        else if (IsRogue)
                        {
                            playerText = $" Rogue [{gamePlayer.FormattedDistance}] ";
                            playerColor = Color.green;
                        }

                        else
                        {
                            bool IsRussianC = Main.IsRussianC(gamePlayer.Player.Profile.Info.Nickname.Localized());
                            if (!IsRussianC)
                                playerText = $" {gamePlayer.Player.Profile.Info.Nickname} [{gamePlayer.FormattedDistance}] ";
                            playerColor = _playerColor;
                        }



                    }
                    else if (gamePlayer.Player.Profile.Side.CheckSide(EPlayerSideMask.Savage))
                    {
                        bool IsCultist = Main.IsCultist(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsCultistFollower = Main.IsCultistfollower(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsGuard = Main.IsGuard(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        bool IsRussianC = Main.IsRussianC(gamePlayer.Player.Profile.Info.Nickname.Localized());
                        if (IsGuard)
                        {
                            playerText = $"Guard [{gamePlayer.FormattedDistance}] ";
                            playerColor = Color.red;
                        }
                        else if (IsCultist)
                        {
                            playerText = $" Cultist Priest [{gamePlayer.FormattedDistance}] ";
                            playerColor = Color.red;
                        }
                        else if (IsCultistFollower)
                        {
                            playerText = $" Cultist[{gamePlayer.FormattedDistance}] ";
                            playerColor = Color.red;
                        }
                        else
                        {
                            playerText = $" Scav [{gamePlayer.FormattedDistance}]  ";
                            playerColor = _botColor;
                            if (!IsRussianC)
                            {
                                playerText = $" Scav [{gamePlayer.FormattedDistance}]  ";
                                playerColor = _botColor;
                            }
                        }

                    }
                    else
                    {
                        playerText = $" Scav [{gamePlayer.FormattedDistance}]  ";
                        playerColor = _botColor;
                    }
                    var playerTextVector = GUI.skin.GetStyle(playerText).CalcSize(new GUIContent(playerText));
                    Render.DrawString(new Vector2(gamePlayer.ScreenPosition.x - (playerTextVector.x / 2f), (gamePlayer.HeadScreenPosition.y - 20f)), playerText, playerColor);
                }

                if (Settings.DrawPlayerLine)
                {
                    Render.DrawLine(new Vector2(Screen.width / 2, Screen.height), new Vector2(gamePlayer.ScreenPosition.x, gamePlayer.ScreenPosition.y), 1.5f, /*GameUtils.IsVisible(destination)*/false ? Color.green : Color.red);

                }
                if (Main.LocalPlayer != null)
                    if (Main.LocalPlayer?.HandsController?.Item is Weapon)
                        Main.LocalPlayerWeapon = (Weapon)Main.LocalPlayer?.HandsController?.Item;
                if (Settings.playerWeapon)
                {
                    if (gamePlayer.Player.HandsController != null)
                    {
                        if (gamePlayer.Player.HandsController.WeaponRoot != null)
                        {
                            if (Settings.playerWeapon)
                                Render.DrawString(new Vector2(gamePlayer.ScreenPosition.x, gamePlayer.ScreenPosition.y + 5f), gamePlayer.Player.HandsController.Item.Name.Localized(), playerColor);
                        }

                    }
                }

                if (Settings.PlayerInventoryESP)
                {
                    int x = -20;
                    HashSet<string> drawnItems = new HashSet<string>();
                    Dictionary<string, int> itemCounts = new Dictionary<string, int>();

                    _equipItemList = gamePlayer.Player.Profile.Inventory.Equipment.GetAllItems().GetEnumerator();

                    while (_equipItemList.MoveNext())
                    {
                        if (_equipItemList.Current != null)
                        {
                            foreach (var item in _equipItemList.Current.GetAllItems())
                            {
                                string itemName = item.LocalizedName();


                                if (!itemCounts.ContainsKey(itemName))
                                    itemCounts[itemName] = 0;
                                itemCounts[itemName]++;

                                bool IsSearchedItem = Main.SearchedItem(itemName);
                                bool IsKappa = Main.IsKappa(itemName);
                                bool IsSuperrare = Main.IsSuperrare(itemName);

                                bool shouldDrawItem = (Settings.searchItem && IsSearchedItem) ||
                                                      (Settings.kappa && IsKappa) ||
                                                      (Settings.superrare && IsSuperrare);

                                if (shouldDrawItem)
                                {

                                    if (!drawnItems.Contains(itemName))
                                    {
                                        Color itemColor = Color.red;

                                        if (Settings.searchItem && IsSearchedItem)
                                        {
                                            itemColor = Color.blue;
                                        }
                                        else if (Settings.kappa && IsKappa)
                                        {
                                            itemColor = Color.green;
                                        }


                                        string itemCountText = itemCounts[itemName] > 1 ? $" ({itemCounts[itemName]}) " : "";
                                        Render.DrawString(new Vector2(gamePlayer.ScreenPosition.x, gamePlayer.ScreenPosition.y - x),
                                                          $"Has: {item.LocalizedShortName()}{itemCountText}. ",
                                                          itemColor);

                                        drawnItems.Add(itemName);
                                        x -= 20;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        private void Chams()
        {
            if (Main.GameWorld != null)
            {
                foreach (GamePlayer gamePlayer in Main.GamePlayers)
                {
                    if (gamePlayer.Distance > Settings.DrawPlayersDistance || gamePlayer.Player == Main.LocalPlayer)
                        continue;


                    if (processedEntities.Contains(gamePlayer.Player.GetInstanceID()))
                        continue;

                    if (Settings.Chams)
                    {
                        Log("Chams On !");
                        var Skins = gamePlayer.Player.PlayerBody.BodySkins.Values;
                        foreach (var Skin in Skins)
                        {
                            foreach (var Renderer in Skin.GetRenderers())
                            {
                                var Material = Renderer.material;
                                Log("Attempting to load Chams shader from AssetBundle.");

                                Shader chamsShader = Main.bundle.LoadAsset<Shader>("Chams.shader");
                                if (chamsShader == null)
                                {
                                    Log("Failed to load Chams.shader from AssetBundle!");
                                    continue;
                                }

                                Log("Chams shader loaded successfully.");

                                var name = Material.name;
                                if (!name.Contains("armour") || !name.Contains("helmet") || !name.Contains("glasses") || !name.Contains("cover") || !name.Contains("rig") || !name.Contains("belt") || !name.Contains("wear") || !name.Contains("mask") || !name.Contains("balaclava"))
                                {
                                    Material.shader = chamsShader;
                                    Material.SetColor("_ColorVisible", new Color(Settings.VisR / 255f, Settings.VisG / 255f, Settings.VisB / 255f, 1f));
                                    Material.SetColor("_ColorBehind", new Color(Settings.HidR / 255f, Settings.HidG / 255f, Settings.HidB / 255f, 1f));
                                }

                            }
                        }
                        processedEntities.Add(gamePlayer.Player.GetInstanceID());
                    }
                }
            }
        }


    }
}