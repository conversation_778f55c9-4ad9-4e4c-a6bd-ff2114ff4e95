﻿
using System.Collections.Generic;
using UnityEngine;

namespace stupid.solutions.Utils
{
    public static class Render
    {
        private static Color _texturesColor;
        public static Material DrawMaterial = new Material(Shader.Find("Hidden/Internal-Colored"));
        private static readonly Texture2D _texture = new Texture2D(1, 1, TextureFormat.ARGB32, false) { filterMode = 0 };
        private static Texture2D _currentTexture;
        private static Color _currentTextureColor = Color.black;
        private static readonly Texture2D Texture2D = new Texture2D(1, 1, TextureFormat.ARGB32, false);
        private static readonly Texture2D Test = new Texture2D(2, 2, TextureFormat.ARGB32, false);
        public static Material material;
        public static GUIStyle StringStyle { get; set; } = new GUIStyle(GUI.skin.label);
        private class RingArray
        {
            public Vector2[] Positions { get; private set; }

            public RingArray(int numSegments)
            {
                Positions = new Vector2[numSegments];
                var stepSize = 360f / numSegments;
                for (int i = 0; i < numSegments; i++)
                {
                    var rad = Mathf.Deg2Rad * stepSize * i;
                    Positions[i] = new Vector2(Mathf.Sin(rad), Mathf.Cos(rad));
                }
            }
        }

        private static Dictionary<int, RingArray> ringDict = new Dictionary<int, RingArray>();

        public static Color Color
        {
            get { return GUI.color; }
            set { GUI.color = value; }
        }

        public static void DrawLine(Vector2 from, Vector2 to, float thickness, Color color)
        {
            Color = color;
            DrawLine(from, to, thickness);
        }
        public static void DrawTexture(Rect position, Texture2D texture)
        {
            if (texture == null)
                return;

            GUI.DrawTexture(position, texture);
        }
        public static void DrawLine(Vector2 from, Vector2 to, float thickness)
        {
            var delta = (to - from).normalized;
            var angle = Mathf.Atan2(delta.y, delta.x) * Mathf.Rad2Deg;
            GUIUtility.RotateAroundPivot(angle, from);
            DrawBox(from, Vector2.right * (from - to).magnitude, thickness, false);
            GUIUtility.RotateAroundPivot(-angle, from);
        }

        public static void DrawBox(float x, float y, float w, float h, Color color)
        {
            DrawLine(new Vector2(x, y), new Vector2(x + w, y), 1f, color);
            DrawLine(new Vector2(x, y), new Vector2(x, y + h), 1f,color);
            DrawLine(new Vector2(x + w, y), new Vector2(x + w, y + h), 1f, color);
            DrawLine(new Vector2(x, y + h), new Vector2(x + w, y + h), 1f, color);
        }

        public static void DrawBox(Vector2 position, Vector2 size, float thickness, Color color, bool centered = true)
        {
            Color = color;
            DrawBox(position, size, thickness, centered);
        }
        public static void DrawBox(Vector2 position, Vector2 size, float thickness, bool centered = true)
        {
            var upperLeft = centered ? position - size / 2f : position;
            GUI.DrawTexture(new Rect(position.x, position.y, size.x, thickness), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(position.x, position.y, thickness, size.y), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(position.x + size.x, position.y, thickness, size.y), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(position.x, position.y + size.y, size.x + thickness, thickness), Texture2D.whiteTexture);
        }
        public static void DrawCornerBox(Vector2 position, Vector2 size, float thickness, Color color, float cornerLength = 10f, bool centered = true)
        {
            var upperLeft = centered ? position - size / 2f : position;

            // Set the color for drawing
            GUI.color = color;

            // Upper left corner
            GUI.DrawTexture(new Rect(upperLeft.x, upperLeft.y, cornerLength, thickness), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(upperLeft.x, upperLeft.y, thickness, cornerLength), Texture2D.whiteTexture);

            // Upper right corner
            GUI.DrawTexture(new Rect(upperLeft.x + size.x - cornerLength, upperLeft.y, cornerLength, thickness), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(upperLeft.x + size.x - thickness, upperLeft.y, thickness, cornerLength), Texture2D.whiteTexture);

            // Bottom left corner
            GUI.DrawTexture(new Rect(upperLeft.x, upperLeft.y + size.y - cornerLength, thickness, cornerLength), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(upperLeft.x, upperLeft.y + size.y - thickness, cornerLength, thickness), Texture2D.whiteTexture);

            // Bottom right corner
            GUI.DrawTexture(new Rect(upperLeft.x + size.x - cornerLength, upperLeft.y + size.y - thickness, cornerLength, thickness), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(upperLeft.x + size.x - thickness, upperLeft.y + size.y - cornerLength, thickness, cornerLength), Texture2D.whiteTexture);

            // Reset color
            GUI.color = Color.white;
        }
        public static void DrawCross(Vector2 position, Vector2 size, float thickness, Color color)
        {
            Color = color;
            DrawCross(position, size, thickness);
        }
        public static void DrawCross(Vector2 position, Vector2 size, float thickness)
        {
            GUI.DrawTexture(new Rect(position.x - size.x / 2f, position.y, size.x, thickness), Texture2D.whiteTexture);
            GUI.DrawTexture(new Rect(position.x, position.y - size.y / 2f, thickness, size.y), Texture2D.whiteTexture);
        }

        public static void DrawDot(Vector2 position, Color color)
        {
            Color = color;
            DrawDot(position);
        }
        public static void DrawDot(Vector2 position)
        {
            DrawBox(position - Vector2.one, Vector2.one * 2f, 1f);
        }

        public static void DrawString(Vector2 position, string label, Color color, bool centered = true)
        {
            Color = color;
            DrawString(position, label, centered);
        }
        public static void DrawString(Vector2 position, string label, bool centered = true)
        {
            var content = new GUIContent(label);
            var size = StringStyle.CalcSize(content);
            var upperLeft = centered ? position - size / 2f : position;
            GUI.Label(new Rect(upperLeft, size), content);
        }

        public static void DrawCircle(Vector2 position, float radius, int numSides, bool centered = true, float thickness = 1f)
        {
            DrawCircle(position, radius, numSides, Color.white, centered, thickness);
        }
        public static void DrawCircle(Vector2 position, float radius, int numSides, Color color, bool centered = true, float thickness = 1f)
        {
            RingArray arr;
            if (ringDict.ContainsKey(numSides))
                arr = ringDict[numSides];
            else
                arr = ringDict[numSides] = new RingArray(numSides);


            var center = centered ? position : position + Vector2.one * radius;

            for (int i = 0; i < numSides - 1; i++)
                DrawLine(center + arr.Positions[i] * radius, center + arr.Positions[i + 1] * radius, thickness, color);

            DrawLine(center + arr.Positions[0] * radius, center + arr.Positions[arr.Positions.Length - 1] * radius, thickness, color);
        }

        public static void DrawSnapline(Vector3 worldpos, Color color)
        {
            Vector3 pos = Main.MainCamera.WorldToScreenPoint(worldpos);
            pos.y = Screen.height - pos.y;
            GL.PushMatrix();
            GL.Begin(1);
            DrawMaterial.SetPass(0);
            GL.Color(color);
            GL.Vertex3(Screen.width / 2, Screen.height, 0f);
            GL.Vertex3(pos.x, pos.y, 0f);
            GL.End();
            GL.PopMatrix();
        }
        public static void DrawCornerBox(Vector2 headPosition, float width, float height, Color color, bool outline)
        {
            int num = (int)(width / 4f);
            int num2 = num;

            if (outline)
            {
                RectFilled(headPosition.x - width / 2f - 1f, headPosition.y - 1f, num + 2, 3f, Color.black);
                RectFilled(headPosition.x - width / 2f - 1f, headPosition.y - 1f, 3f, num2 + 2, Color.black);
                RectFilled(headPosition.x + width / 2f - num - 1f, headPosition.y - 1f, num + 2, 3f, Color.black);
                RectFilled(headPosition.x + width / 2f - 1f, headPosition.y - 1f, 3f, num2 + 2, Color.black);
                RectFilled(headPosition.x - width / 2f - 1f, headPosition.y + height - 4f, num + 2, 3f, Color.black);
                RectFilled(headPosition.x - width / 2f - 1f, headPosition.y + height - num2 - 4f, 3f, num2 + 2, Color.black);
                RectFilled(headPosition.x + width / 2f - num - 1f, headPosition.y + height - 4f, num + 2, 3f, Color.black);
                RectFilled(headPosition.x + width / 2f - 1f, headPosition.y + height - num2 - 4f, 3f, num2 + 3, Color.black);
            }
            RectFilled(headPosition.x - width / 2f, headPosition.y, num, 1f, color);
            RectFilled(headPosition.x - width / 2f, headPosition.y, 1f, num2, color);
            RectFilled(headPosition.x + width / 2f - num, headPosition.y, num, 1f, color);
            RectFilled(headPosition.x + width / 2f, headPosition.y, 1f, num2, color);
            RectFilled(headPosition.x - width / 2f, headPosition.y + height - 3f, num, 1f, color);
            RectFilled(headPosition.x - width / 2f, headPosition.y + height - num2 - 3f, 1f, num2, color);
            RectFilled(headPosition.x + width / 2f - num, headPosition.y + height - 3f, num, 1f, color);
            RectFilled(headPosition.x + width / 2f, headPosition.y + height - num2 - 3f, 1f, num2 + 1, color);
        }
        public static void RectFilled(float x, float y, float width, float height, Color color)
        {
            if (color != _texturesColor)
            {
                _texturesColor = color;
                _texture.SetPixel(0, 0, color);
                _texture.Apply();
            }
            GUI.DrawTexture(new Rect(x, y, width, height), _texture);
        }
        public static void BoxRect(Rect rect, Color color)
        {
            if (_currentTexture == null)
            {
                _currentTexture = new Texture2D(1, 1);
                _currentTexture.SetPixel(0, 0, color);
                _currentTexture.Apply();
                _currentTextureColor = color;
            }
            else if (color != _currentTextureColor)
            {
                _currentTexture.SetPixel(0, 0, color);
                _currentTexture.Apply();
                _currentTextureColor = color;
            }
            GUI.DrawTexture(rect, _currentTexture);
        }
        public static void DrawRadarBackground(Rect rect)
        {
            Color color = new Color(0f, 0f, 0f, 0.5f);
            Texture2D.SetPixel(0, 0, color);
            Texture2D.Apply();
            GUI.color = color;
            GUI.DrawTexture(rect, Texture2D);
        }
        public static void MatrixLine(Vector3 start, Vector3 end, Color color)
        {
            material.SetPass(0);
            GL.PushMatrix();
            GL.LoadProjectionMatrix(Main.MainCamera.projectionMatrix);
            GL.modelview = Main.MainCamera.worldToCameraMatrix;
            GL.Begin(1);
            GL.Color(color);
            GL.Vertex(start);
            GL.Vertex(end);
            GL.End();
            GL.PopMatrix();
        }

    }
}
