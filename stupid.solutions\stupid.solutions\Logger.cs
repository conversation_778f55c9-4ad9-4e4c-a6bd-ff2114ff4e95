﻿using System;
using System.IO;
using UnityEngine;

public static class Logger
{
    private static readonly string logFilePath = Path.Combine(Application.persistentDataPath, "log3.txt");

    // Log levels
    public enum LogLevel
    {
        Info,
        Warning,
        Error
    }

    public static void Log(string message, LogLevel level = LogLevel.Info)
    {
        try
        {
            string logMessage = FormatLogMessage(message, level);
            WriteLogToFile(logMessage);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to log message: {ex.Message}");
        }
    }

    private static string FormatLogMessage(string message, LogLevel level)
    {
        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        return $"{timestamp} [{level}] {message}";
    }

    private static void WriteLogToFile(string message)
    {
        // Append text to the log file, creating it if it does not exist
        using (StreamWriter sw = new StreamWriter(logFilePath, true))
        {
            sw.WriteLine(message);
        }
    }

    public static void ClearLog()
    {
        try
        {
            File.Delete(logFilePath);
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to clear log file: {ex.Message}");
        }
    }
}