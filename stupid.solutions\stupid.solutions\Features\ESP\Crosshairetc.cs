﻿using stupid.solutions.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using stupid.solutions;
using Comfort.Common;
using EFT;
using EFT.Interactive;
using stupid.solutions.Data;
using JsonType;
using stupid.solutions.Features;
using stupid.solutions.enums;
using EFT.InventoryLogic;


namespace stupid.solutions.Features.ESP
{
    public class Crosshairetc : MonoBehaviour
    {
        public float lineLength = 20f;   
        public float lineThickness = 2f;  
        private Texture2D crosshairTexture;
        //private static Item _tempItem;
        //private static IEnumerator<Item> _equipItemList;
        //Color ItemColor = Color.white;
        //private static List<Item> _equipItemList = new List<Item>();

        void Start()
        {
            crosshairTexture = new Texture2D(1, 1);
            crosshairTexture.SetPixel(0, 0, Color.red); 
            crosshairTexture.Apply();
            //StartCoroutine(UpdateItemListRegularly());
        }
       
       
        public void OnGUI()
        {
            
            if (Settings.AimbotDrawFOV)
            {
                Color purple = new Color(0.5f, 0f, 0.5f, 1f);
                Render.DrawCircle(new Vector2(Screen.width / 2, Screen.height / 2), Settings.AimbotFOV * 15.7f, 32, purple, true, 1.25f);
            }
            if (Settings.Crosshair)
            {
                {
                    float centerX = Screen.width / 2;
                    float centerY = Screen.height / 2;
                   

                    GUI.DrawTexture(new Rect(centerX - lineLength / 2, centerY - lineThickness / 2, lineLength, lineThickness), crosshairTexture);

                    
                    GUI.DrawTexture(new Rect(centerX - lineThickness / 2, centerY - lineLength / 2, lineThickness, lineLength), crosshairTexture);
                    

                }
            }
            
            foreach (GamePlayer gamePlayer in Main.GamePlayers)
            {
                if (!gamePlayer.IsOnScreen || gamePlayer.Distance > Settings.DrawPlayersDistance || gamePlayer.Player == Main.LocalPlayer)
                    continue;
                
                if (Settings.DrawPlayers)
                {
                    if (Settings.EnableSkeleton && Settings.DrawPlayers)
                    {
                        DrawSkeleton(gamePlayer.Player);
                        
                        
                    }
                    
                }
                if (Settings.drawaimpos && Settings.DrawPlayers)
                {
                    DrawAim(gamePlayer);
                }
                
            }
            

            //GUI.Label(new Rect(10f, 10f, 300f, 100f), "stupid.solutions"); //legacy watermark
            
        }
        private static void DrawAim(GamePlayer gamePlayer)
        {
            Vector3 endPosition = GameUtils.WorldPointToScreenPoint(RayCast.BarrelRayCast(gamePlayer.Player));
            Vector3 startPosition = GameUtils.WorldPointToScreenPoint(gamePlayer.Player.Fireport.position);
            Render.DrawLine(startPosition, endPosition, 1f, Color.red);
        }
        
        private static Dictionary<HumanBones, Vector3> GetBones(Player gamePlayer)
        {
            Dictionary<HumanBones, Vector3> dictionary = new Dictionary<HumanBones, Vector3>();
            if (gamePlayer.PlayerBody == null || gamePlayer.PlayerBody.SkeletonRootJoint == null)
            {
                return dictionary;
            }
            List<Transform> list = gamePlayer.PlayerBody.SkeletonRootJoint.Bones.Values.ToList();
            if (list.Count == 0)
            {
                return dictionary;
            }
            for (int i = 0; i < list.Count; i++)
            {
                Transform transform = list[i];
                if (!(transform == null) && NeededBones.Contains((HumanBones)i))
                {
                    Vector3 vector = Main.MainCamera.WorldToScreenPoint(transform.position);
                    if (GameUtils.IsScreenPointVisible(vector))
                    {
                        vector.y = Screen.height - vector.y;
                        dictionary[(HumanBones)i] = vector;
                    }
                }
            }
            return dictionary;
        }
        private static void DrawSkeleton(Player gamePlayer)
        {

            Dictionary<HumanBones, Vector3> bones = GetBones(gamePlayer);
            if (bones.Count != 0)
            {
                ConnectBones(bones, HumanBones.HumanPelvis, HumanBones.HumanLThigh1);
                ConnectBones(bones, HumanBones.HumanLThigh1, HumanBones.HumanLThigh2);
                ConnectBones(bones, HumanBones.HumanLThigh2, HumanBones.HumanLCalf);
                ConnectBones(bones, HumanBones.HumanLCalf, HumanBones.HumanLFoot);
                ConnectBones(bones, HumanBones.HumanLFoot, HumanBones.HumanLToe);
                ConnectBones(bones, HumanBones.HumanPelvis, HumanBones.HumanRThigh1);
                ConnectBones(bones, HumanBones.HumanRThigh1, HumanBones.HumanRThigh2);
                ConnectBones(bones, HumanBones.HumanRThigh2, HumanBones.HumanRCalf);
                ConnectBones(bones, HumanBones.HumanRCalf, HumanBones.HumanRFoot);
                ConnectBones(bones, HumanBones.HumanRFoot, HumanBones.HumanRToe);
                ConnectBones(bones, HumanBones.HumanPelvis, HumanBones.HumanSpine1);
                ConnectBones(bones, HumanBones.HumanSpine1, HumanBones.HumanSpine2);
                ConnectBones(bones, HumanBones.HumanSpine2, HumanBones.HumanSpine3);
                ConnectBones(bones, HumanBones.HumanSpine3, HumanBones.HumanNeck);
                ConnectBones(bones, HumanBones.HumanNeck, HumanBones.HumanHead);
                ConnectBones(bones, HumanBones.HumanSpine3, HumanBones.HumanLCollarbone);
                ConnectBones(bones, HumanBones.HumanLCollarbone, HumanBones.HumanLForearm1);
                ConnectBones(bones, HumanBones.HumanLForearm1, HumanBones.HumanLForearm2);
                ConnectBones(bones, HumanBones.HumanLForearm2, HumanBones.HumanLForearm3);
                ConnectBones(bones, HumanBones.HumanLForearm3, HumanBones.HumanLPalm);
                ConnectBones(bones, HumanBones.HumanLPalm, HumanBones.HumanLDigit11);
                ConnectBones(bones, HumanBones.HumanLDigit11, HumanBones.HumanLDigit12);
                ConnectBones(bones, HumanBones.HumanLDigit12, HumanBones.HumanLDigit13);
                ConnectBones(bones, HumanBones.HumanSpine3, HumanBones.HumanRCollarbone);
                ConnectBones(bones, HumanBones.HumanRCollarbone, HumanBones.HumanRForearm1);
                ConnectBones(bones, HumanBones.HumanRForearm1, HumanBones.HumanRForearm2);
                ConnectBones(bones, HumanBones.HumanRForearm2, HumanBones.HumanRForearm3);
                ConnectBones(bones, HumanBones.HumanRForearm3, HumanBones.HumanRPalm);
                ConnectBones(bones, HumanBones.HumanRPalm, HumanBones.HumanRDigit11);
                ConnectBones(bones, HumanBones.HumanRDigit11, HumanBones.HumanRDigit12);
                ConnectBones(bones, HumanBones.HumanRDigit12, HumanBones.HumanRDigit13);
            }
        }
        private static void ConnectBones(Dictionary<HumanBones, Vector3> bones, HumanBones start, HumanBones stop)
        {

            if (bones.ContainsKey(start) && bones.ContainsKey(stop) && GameUtils.IsScreenPointVisible(bones[start]) && GameUtils.IsScreenPointVisible(bones[stop]))
            {
                Render.DrawLine(bones[start], bones[stop], 1f, Color.gray);
            }

        }
        private static readonly List<HumanBones> NeededBones = new List<HumanBones>
        {
            HumanBones.HumanPelvis,
            HumanBones.HumanLThigh1,
            HumanBones.HumanLThigh2,
            HumanBones.HumanLCalf,
            HumanBones.HumanLFoot,
            HumanBones.HumanLToe,
            HumanBones.HumanPelvis,
            HumanBones.HumanRThigh1,
            HumanBones.HumanRThigh2,
            HumanBones.HumanRCalf,
            HumanBones.HumanRFoot,
            HumanBones.HumanRToe,
            HumanBones.HumanSpine1,
            HumanBones.HumanSpine2,
            HumanBones.HumanSpine3,
            HumanBones.HumanNeck,
            HumanBones.HumanHead,
            HumanBones.HumanLCollarbone,
            HumanBones.HumanLForearm1,
            HumanBones.HumanLForearm2,
            HumanBones.HumanLForearm3,
            HumanBones.HumanLPalm,
            HumanBones.HumanLDigit11,
            HumanBones.HumanLDigit12,
            HumanBones.HumanLDigit13,
            HumanBones.HumanRCollarbone,
            HumanBones.HumanRForearm1,
            HumanBones.HumanRForearm2,
            HumanBones.HumanRForearm3,
            HumanBones.HumanRPalm,
            HumanBones.HumanRDigit11,
            HumanBones.HumanRDigit12,
            HumanBones.HumanRDigit13
        };
        

    }
}
